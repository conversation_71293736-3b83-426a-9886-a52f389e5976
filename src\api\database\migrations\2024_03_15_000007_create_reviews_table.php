<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();

            // Clé étrangère userId avec suppression en cascade
            $table->foreignId('userId')
                  ->constrained('users')
                  ->onDelete('cascade');

            // Clé étrangère salonId avec suppression en cascade
            $table->unsignedBigInteger('salonId');
            $table->foreign('salonId')
                  ->references('id')
                  ->on('salons')
                  ->onDelete('cascade');

            // Clé étrangère bookingId avec suppression en cascade
            $table->foreignId('bookingId')
                  ->constrained('bookings')
                  ->onDelete('cascade');

            $table->decimal('rating', 3, 2);
            $table->text('comment');
            $table->enum('source', ['google', 'trustpilot', 'internal']);
            $table->json('details')->nullable();
            $table->boolean('verified')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('reviews');
    }
};
