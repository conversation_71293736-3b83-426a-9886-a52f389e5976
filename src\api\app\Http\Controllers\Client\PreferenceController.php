<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\UserPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PreferenceController extends Controller
{
    public function show()
    {
        $preferences = Auth::user()->preferences;
        return response()->json($preferences);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'preferred_salons' => 'sometimes|array',
            'preferred_salons.*' => 'exists:salons,_id',
            'preferred_hairdressers' => 'sometimes|array',
            'preferred_hairdressers.*' => 'exists:users,_id',
            'preferred_services' => 'sometimes|array',
            'preferred_services.*' => 'exists:services,_id',
            'preferred_days' => 'sometimes|array',
            'preferred_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'preferred_times' => 'sometimes|array',
            'preferred_times.*' => 'date_format:H:i',
            'notification_preferences' => 'sometimes|array',
            'notification_preferences.email' => 'boolean',
            'notification_preferences.sms' => 'boolean',
            'notification_preferences.push' => 'boolean',
        ]);

        $preferences = Auth::user()->preferences()->updateOrCreate(
            ['userId' => Auth::id()],
            $validated
        );

        return response()->json($preferences);
    }

    public function updateNotifications(Request $request)
    {
        $validated = $request->validate([
            'email_notifications' => 'required|boolean',
            'sms_notifications' => 'required|boolean',
            'push_notifications' => 'required|boolean',
            'reminder_time' => 'required|integer|min:1|max:72', // hours before appointment
        ]);

        $preferences = Auth::user()->preferences()->updateOrCreate(
            ['userId' => Auth::id()],
            ['notification_preferences' => $validated]
        );

        return response()->json($preferences);
    }
}