<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'salonId' => $this->salonId,
            'userId' => $this->userId,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'source' => $this->source,
            'date' => $this->date,
            'user' => new UserResource($this->whenLoaded('user')),
            'salon' => new SalonResource($this->whenLoaded('salon')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}