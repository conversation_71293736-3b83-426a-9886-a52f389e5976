<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;
use Illuminate\Support\Facades\Auth;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $query = Booking::where('salonId', $salon->id)
            ->with(['client', 'hairdresser', 'services']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date')) {
            $query->whereDate('dateTime', $request->date);
        }

        if ($request->has('hairdresserid')) {
            $query->where('hairdresserId', $request->hairdresserid);
        }

        $bookings = $query->paginate(10);
        return BookingResource::collection($bookings);
    }

    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);
        return new BookingResource($booking->load(['client', 'hairdresser', 'services']));
    }

    public function update(Request $request, Booking $booking)
    {
        $this->authorize('manage', $booking);

        $validated = $request->validate([
            'status' => 'sometimes|in:confirmed,cancelled,completed,pending',
            'dateTime' => 'sometimes|date|after:now',
            'hairdresserId' => 'sometimes|exists:hairdressers,id',
            'services' => 'sometimes|array',
            'services.*' => 'exists:services,id',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);

        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return new BookingResource($booking);
    }

    public function getDailySchedule(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $date = $request->get('date', today());

        $schedule = User::where('salonId', $salon->id)
            ->where('role', 'hairdresser')
            ->with(['bookings' => function ($query) use ($date) {
                $query->whereDate('dateTime', $date)
                      ->with(['client', 'services']);
            }])
            ->get();

        return response()->json($schedule);
    }

    public function reassignHairdresser(Request $request, Booking $booking)
    {
        $this->authorize('manage', $booking);

        $validated = $request->validate([
            'hairdresserId' => 'required|exists:users,id',
            'reason' => 'required|string',
        ]);

        $booking->update([
            'hairdresserId' => $validated['hairdresserId'],
            'reassignment_reason' => $validated['reason'],
        ]);

        // Notify both client and hairdressers
        $booking->client->notify(new BookingReassigned($booking));
        $booking->hairdresser->notify(new BookingReassigned($booking));

        return new BookingResource($booking);
    }
}