<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Hairdresser\{
    HairdresserDashboardController,
    AppointmentController,
    ClientController,
    AvailabilityController,
    PerformanceController,
    BookingController
};

/*
|--------------------------------------------------------------------------
| Hairdresser API Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['auth:sanctum', 'role:hairdresser'])->group(function () {
    // Dashboard
    Route::get('/dashboard/stats', [HairdresserDashboardController::class, 'getStats']);

    // Appointment Management
    Route::prefix('appointments')->group(function () {
        Route::get('/', [AppointmentController::class, 'index']);
        Route::get('/{booking}', [AppointmentController::class, 'show']);
        Route::put('/{booking}', [AppointmentController::class, 'update']);
        Route::get('/daily-schedule', [AppointmentController::class, 'getDailySchedule']);
        Route::get('/weekly-schedule', [AppointmentController::class, 'getWeeklySchedule']);
    });

    // Client Management
    Route::prefix('clients')->group(function () {
        Route::get('/', [ClientController::class, 'index']);
        Route::get('/{client}', [ClientController::class, 'show']);
        Route::post('/{client}/notes', [ClientController::class, 'storeNote']);
        Route::put('/notes/{note}', [ClientController::class, 'updateNote']);
        Route::delete('/notes/{note}', [ClientController::class, 'deleteNote']);
    });

    // Availability Management (Legacy)
    Route::prefix('availability')->group(function () {
        Route::get('/', [AvailabilityController::class, 'show']);
        Route::put('/', [AvailabilityController::class, 'update']);
        Route::post('/time-off', [AvailabilityController::class, 'requestTimeOff']);
        Route::get('/time-off', [AvailabilityController::class, 'getTimeOffRequests']);
    });

    // Time Slots Management
    Route::prefix('time-slots')->group(function () {
        Route::get('/templates', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'getAvailabilityTemplates']);
        Route::post('/templates', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'storeAvailabilityTemplate']);
        Route::get('/schedule', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'getSchedule']);
        Route::post('/exceptions', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'createException']);
        Route::get('/exceptions', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'getExceptions']);
        Route::delete('/exceptions/{exception}', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'deleteException']);
        Route::post('/toggle-status', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'toggleSlotStatus']);
        Route::get('/stats', [\App\Http\Controllers\Hairdresser\TimeSlotController::class, 'getStats']);
    });

    // Performance Stats
    Route::prefix('performance')->group(function () {
        Route::get('/stats', [PerformanceController::class, 'getStats']);
    });

    // Booking Management
    Route::prefix('bookings')->group(function () {
        Route::get('/', [BookingController::class, 'index']);
        Route::get('/stats', [BookingController::class, 'getStats']);
        Route::get('/{booking}', [BookingController::class, 'show']);
        Route::post('/{booking}/confirm', [BookingController::class, 'confirm']);
        Route::post('/{booking}/reject', [BookingController::class, 'reject']);
        Route::post('/{booking}/complete', [BookingController::class, 'complete']);
    });


});
