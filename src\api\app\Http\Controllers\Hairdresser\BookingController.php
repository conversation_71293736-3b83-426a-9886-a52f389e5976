<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Afficher la liste des réservations du coiffeur
     */
    public function index(Request $request)
    {
        $query = Booking::where('hairdresserId', Auth::id())
            ->with(['client', 'services', 'salon']);

        // Filtre par statut
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filtre par date
        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('dateTime', $request->date);
        }

        // Filtre par période (optionnel)
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('dateTime', [$request->start_date, $request->end_date]);
        }

        $bookings = $query->orderBy('dateTime', 'desc')->paginate(15);
        
        return response()->json([
            'success' => true,
            'data' => BookingResource::collection($bookings),
            'pagination' => [
                'current_page' => $bookings->currentPage(),
                'last_page' => $bookings->lastPage(),
                'per_page' => $bookings->perPage(),
                'total' => $bookings->total(),
            ]
        ]);
    }

    /**
     * Afficher les détails d'une réservation
     */
    public function show(Booking $booking)
    {
        // Vérifier que le coiffeur peut voir cette réservation
        if ($booking->hairdresserId !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $booking->load(['client', 'services', 'salon', 'review']);

        return response()->json([
            'success' => true,
            'data' => new BookingResource($booking)
        ]);
    }

    /**
     * Confirmer une réservation
     */
    public function confirm(Booking $booking)
    {
        // Vérifier que le coiffeur peut modifier cette réservation
        if ($booking->hairdresserId !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        // Vérifier que la réservation est en attente
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Cette réservation ne peut pas être confirmée'
            ], 400);
        }

        $oldStatus = $booking->status;
        $booking->update(['status' => 'confirmed']);

        // Notifier le client du changement de statut
        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return response()->json([
            'success' => true,
            'message' => 'Réservation confirmée avec succès',
            'data' => new BookingResource($booking->load(['client', 'services', 'salon']))
        ]);
    }

    /**
     * Refuser une réservation
     */
    public function reject(Request $request, Booking $booking)
    {
        // Vérifier que le coiffeur peut modifier cette réservation
        if ($booking->hairdresserId !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        // Vérifier que la réservation est en attente
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Cette réservation ne peut pas être refusée'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Raison de refus requise',
                'errors' => $validator->errors()
            ], 422);
        }

        $oldStatus = $booking->status;
        $booking->update([
            'status' => 'cancelled',
            'cancellation_reason' => $request->cancellation_reason
        ]);

        // Notifier le client du changement de statut
        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return response()->json([
            'success' => true,
            'message' => 'Réservation refusée avec succès',
            'data' => new BookingResource($booking->load(['client', 'services', 'salon']))
        ]);
    }

    /**
     * Marquer une réservation comme terminée
     */
    public function complete(Booking $booking)
    {
        // Vérifier que le coiffeur peut modifier cette réservation
        if ($booking->hairdresserId !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        // Vérifier que la réservation est confirmée
        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Seules les réservations confirmées peuvent être marquées comme terminées'
            ], 400);
        }

        $oldStatus = $booking->status;
        $booking->update(['status' => 'completed']);

        // Notifier le client du changement de statut
        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return response()->json([
            'success' => true,
            'message' => 'Réservation marquée comme terminée',
            'data' => new BookingResource($booking->load(['client', 'services', 'salon']))
        ]);
    }

    /**
     * Obtenir les statistiques des réservations
     */
    public function getStats()
    {
        $hairdresserId = Auth::id();
        
        $stats = [
            'total' => Booking::where('hairdresserId', $hairdresserId)->count(),
            'pending' => Booking::where('hairdresserId', $hairdresserId)->where('status', 'pending')->count(),
            'confirmed' => Booking::where('hairdresserId', $hairdresserId)->where('status', 'confirmed')->count(),
            'completed' => Booking::where('hairdresserId', $hairdresserId)->where('status', 'completed')->count(),
            'cancelled' => Booking::where('hairdresserId', $hairdresserId)->where('status', 'cancelled')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
} 