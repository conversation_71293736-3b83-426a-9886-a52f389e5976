<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\SalonResource;
use Illuminate\Support\Facades\Auth;

class SalonController extends Controller
{
    public function index()
    {
        $salons = Auth::user()->salons()->with(['services', 'hairdressers'])->get();
        return SalonResource::collection($salons);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'hours' => 'required|array',
            'contact' => 'required|array',
            'contact.phone' => 'required|string',
            'contact.email' => 'required|email',
            'managerId' => 'nullable|exists:users,id',
        ]);

        // Ajouter automatiquement l'ownerId
        $validated['ownerId'] = Auth::id();

        $salon = Auth::user()->salons()->create($validated);
        return new SalonResource($salon);
    }

    public function show(Salon $salon)
    {
        $this->authorize('view', $salon);
        return new SalonResource($salon->load(['services', 'hairdressers', 'reviews']));
    }

    public function update(Request $request, Salon $salon)
    {
        $this->authorize('update', $salon);

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'address' => 'sometimes|string',
            'hours' => 'sometimes|array',
            'contact' => 'sometimes|array',
            'contact.phone' => 'sometimes|string',
            'contact.email' => 'sometimes|email',
            'managerId' => 'nullable|exists:users,id',
        ]);

        $salon->update($validated);
        return new SalonResource($salon);
    }

    public function getAnalytics(Salon $salon)
    {
        $this->authorize('view', $salon);

        $analytics = [
            'total_bookings' => $salon->bookings()->count(),
            'total_revenue' => $salon->bookings()->sum('total_amount'),
            'average_rating' => $salon->reviews()->avg('rating'),
            'popular_services' => $salon->services()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'top_hairdressers' => $salon->hairdressers()
                ->withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
        ];

        return response()->json($analytics);
    }
}