<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;
use Illuminate\Support\Facades\Auth;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        $query = Booking::where('hairdresserId', Auth::id())
            ->with(['client', 'services']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date')) {
            $query->whereDate('dateTime', $request->date);
        }

        $bookings = $query->orderBy('dateTime')->paginate(10);
        return BookingResource::collection($bookings);
    }

    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);
        return new BookingResource($booking->load(['client', 'services']));
    }

    public function update(Request $request, Booking $booking)
    {
        $this->authorize('update', $booking);

        $validated = $request->validate([
            'status' => 'required|in:confirmed,completed,cancelled',
            'notes' => 'sometimes|string',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);

        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return new BookingResource($booking);
    }

    public function getDailySchedule(Request $request)
    {
        $date = $request->get('date', today());

        $schedule = Booking::where('hairdresserId', Auth::id())
            ->whereDate('dateTime', $date)
            ->with(['client', 'services'])
            ->orderBy('dateTime')
            ->get();

        return response()->json($schedule);
    }

    public function getWeeklySchedule(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfWeek());
        $endDate = $request->get('end_date', now()->endOfWeek());

        $schedule = Booking::where('hairdresserId', Auth::id())
            ->whereBetween('dateTime', [$startDate, $endDate])
            ->with(['client', 'services'])
            ->orderBy('dateTime')
            ->get();

        return response()->json($schedule);
    }
}