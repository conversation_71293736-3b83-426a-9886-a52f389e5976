<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;

class BookingManagementController extends Controller
{
    public function index(Request $request)
    {
        try {
            \Log::info('Manager BookingManagementController::index appelé');
            
            $query = Booking::with(['client', 'salon', 'hairdresser', 'services']);

            // Filtrer par statut
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            // Filtrer par date
            if ($request->has('date')) {
                $query->whereDate('dateTime', $request->date);
            }

            // Recherche par terme
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = $request->search;
                $query->where(function($q) use ($searchTerm) {
                    $q->whereHas('client', function($clientQuery) use ($searchTerm) {
                        $clientQuery->where('firstName', 'like', "%{$searchTerm}%")
                                    ->orWhere('lastName', 'like', "%{$searchTerm}%")
                                    ->orWhere('email', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('salon', function($salonQuery) use ($searchTerm) {
                        $salonQuery->where('name', 'like', "%{$searchTerm}%");
                    });
                });
            }

            // Pagination
            $perPage = $request->get('limit', 10);
            $bookings = $query->orderBy('dateTime', 'desc')->paginate($perPage);

            \Log::info('Manager - Nombre de bookings trouvés: ' . $bookings->count());

            return response()->json([
                'data' => BookingResource::collection($bookings->items()),
                'total' => $bookings->total(),
                'per_page' => $bookings->perPage(),
                'current_page' => $bookings->currentPage(),
                'last_page' => $bookings->lastPage()
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur dans Manager BookingManagementController::index: ' . $e->getMessage());
            return response()->json([
                'error' => 'Erreur lors de la récupération des bookings',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Booking $booking)
    {
        return new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']));
    }

    public function update(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'status' => 'sometimes|in:confirmed,cancelled,completed,pending',
            'dateTime' => 'sometimes|date|after:now',
            'hairdresserId' => 'sometimes|exists:hairdressers,_id',
            'services' => 'sometimes|array',
            'services.*' => 'exists:services,_id',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);

        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return new BookingResource($booking);
    }

    public function destroy(Booking $booking)
    {
        if ($booking->status === 'completed') {
            return response()->json([
                'message' => 'Cannot delete completed bookings'
            ], 403);
        }

        $booking->delete();
        return response()->json(['message' => 'Booking deleted successfully']);
    }

    public function getAnalytics(Request $request)
    {
        $analytics = [
            'total_bookings' => Booking::count(),
            'completed_bookings' => Booking::where('status', 'completed')->count(),
            'cancelled_bookings' => Booking::where('status', 'cancelled')->count(),
            'pending_bookings' => Booking::where('status', 'pending')->count(),
            'confirmed_bookings' => Booking::where('status', 'confirmed')->count(),
            'revenue' => Booking::where('status', 'completed')->sum('total_amount'),
            'popular_time_slots' => Booking::selectRaw('HOUR(dateTime) as hour, COUNT(*) as count')
                ->groupBy('hour')
                ->orderBy('count', 'desc')
                ->get(),
            'booking_trends' => Booking::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', now()->subDays(30))
                ->groupBy('date')
                ->get(),
        ];

        return response()->json($analytics);
    }

    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'bookings' => 'required|array',
            'bookings.*.id' => 'required|exists:bookings,_id',
            'bookings.*.status' => 'required|in:confirmed,cancelled,completed,pending',
        ]);

        foreach ($validated['bookings'] as $bookingData) {
            $booking = Booking::find($bookingData['id']);
            $oldStatus = $booking->status;

            $booking->update(['status' => $bookingData['status']]);

            if ($booking->status !== $oldStatus) {
                $booking->client->notify(new BookingStatusChanged($booking));
            }
        }

        return response()->json(['message' => 'Bookings updated successfully']);
    }

    /**
     * Confirmer un rendez-vous (accepter)
     */
    public function confirm(Booking $booking)
    {
        // Debug: Afficher le statut actuel
        \Log::info('Manager - Statut actuel du booking ' . $booking->id . ': ' . $booking->status);
        
        // Vérifier que le rendez-vous peut être confirmé (pas déjà confirmé, annulé ou terminé)
        if (in_array($booking->status, ['confirmed', 'cancelled', 'completed'])) {
            return response()->json([
                'message' => 'Ce rendez-vous ne peut pas être confirmé (statut actuel: ' . $booking->status . ')'
            ], 400);
        }

        // Charger les relations nécessaires
        $booking->load(['client', 'salon', 'hairdresser']);

        // Sauvegarder l'ancien statut pour la notification
        $oldStatus = $booking->status;

        // Confirmer le rendez-vous
        $booking->update(['status' => 'confirmed']);

        // Envoyer une notification au client si le statut a changé
        if ($booking->status !== $oldStatus) {
            try {
                $booking->client->notify(new BookingStatusChanged($booking));
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas faire échouer la confirmation
                \Log::error('Erreur lors de l\'envoi de la notification: ' . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Rendez-vous confirmé avec succès',
            'booking' => new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']))
        ]);
    }

    /**
     * Refuser un rendez-vous
     */
    public function reject(Booking $booking)
    {
        // Debug: Afficher le statut actuel
        \Log::info('Manager - Statut actuel du booking ' . $booking->id . ': ' . $booking->status);
        
        // Vérifier que le rendez-vous peut être refusé (pas déjà confirmé, annulé ou terminé)
        if (in_array($booking->status, ['confirmed', 'cancelled', 'completed'])) {
            return response()->json([
                'message' => 'Ce rendez-vous ne peut pas être refusé (statut actuel: ' . $booking->status . ')'
            ], 400);
        }

        // Charger les relations nécessaires
        $booking->load(['client', 'salon', 'hairdresser']);

        // Sauvegarder l'ancien statut pour la notification
        $oldStatus = $booking->status;

        // Refuser le rendez-vous
        $booking->update(['status' => 'cancelled']);

        // Envoyer une notification au client si le statut a changé
        if ($booking->status !== $oldStatus) {
            try {
                $booking->client->notify(new BookingStatusChanged($booking));
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas faire échouer le refus
                \Log::error('Erreur lors de l\'envoi de la notification: ' . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Rendez-vous refusé avec succès',
            'booking' => new BookingResource($booking->load(['client', 'salon', 'hairdresser', 'services']))
        ]);
    }

    /**
     * Méthode temporaire pour réinitialiser les statuts des bookings (pour les tests)
     */
    public function resetBookingStatuses()
    {
        // Réinitialiser tous les bookings en 'pending' pour les tests
        Booking::whereIn('status', ['confirmed', 'cancelled'])->update(['status' => 'pending']);
        
        return response()->json([
            'message' => 'Statuts des bookings réinitialisés en "pending"'
        ]);
    }

    /**
     * Méthode de test pour vérifier que le contrôleur fonctionne
     */
    public function test()
    {
        return response()->json([
            'message' => 'Manager BookingManagementController fonctionne correctement',
            'timestamp' => now()
        ]);
    }
} 