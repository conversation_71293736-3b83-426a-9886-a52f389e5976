<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roles = [
            ['name' => 'admin', 'description' => 'Administrator with full access', 'guard_name' => 'web'],
            ['name' => 'owner', 'description' => 'Owner of the business', 'guard_name' => 'web'],
            ['name' => 'manager', 'description' => 'Manager with limited permissions', 'guard_name' => 'web'],
            ['name' => 'hairdresser', 'description' => 'Hairdresser with access to appointments', 'guard_name' => 'web'],
            ['name' => 'client', 'description' => 'Client with access to booking services', 'guard_name' => 'web'],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->updateOrInsert(
                ['name' => $role['name']],
                [
                    'description' => $role['description'],
                    'guard_name' => $role['guard_name'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );
        }
    }
}
