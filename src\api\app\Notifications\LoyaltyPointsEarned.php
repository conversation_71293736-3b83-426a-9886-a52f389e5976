<?php

namespace App\Notifications;

use App\Models\LoyaltyTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class LoyaltyPointsEarned extends Notification
{
    use Queueable;

    protected $transaction;

    public function __construct(LoyaltyTransaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Loyalty Points Earned!')
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('You have earned ' . $this->transaction->points . ' loyalty points!')
            ->line('Transaction details:')
            ->line($this->transaction->description)
            ->line('Current balance: ' . $notifiable->loyaltyPoints . ' points')
            ->line('These points will expire on: ' . $this->transaction->expires_at->format('F j, Y'))
            ->action('View Rewards', url('/rewards'))
            ->line('Thank you for your continued loyalty!');
    }

    public function toArray($notifiable)
    {
        return [
            'transaction_id' => $this->transaction->id,
            'type' => 'loyalty_points_earned',
            'points' => $this->transaction->points,
            'message' => 'You earned ' . $this->transaction->points . ' loyalty points',
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}