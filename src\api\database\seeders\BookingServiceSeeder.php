<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Booking;
use App\Models\Service;
use Illuminate\Support\Facades\DB;

class BookingServiceSeeder extends Seeder
{
    public function run()
    {
        $bookings = \App\Models\Booking::all();
        $services = \App\Models\Service::all();
    
        if ($bookings->isEmpty() || $services->isEmpty()) {
            $this->command->warn('Pas de réservations ou de services disponibles pour générer des relations.');
            return;
        }
    
        $this->command->info("Nombre de réservations : {$bookings->count()}");
        $this->command->info("Nombre de services : {$services->count()}");
    
        foreach ($bookings as $booking) {
            $selectedServices = $services->random(rand(1, 3));
    
            foreach ($selectedServices as $service) {
                DB::table('booking_services')->insert([
                    'bookingId' => $booking->id,
                    'serviceId' => $service->id,
                    'price_at_time' => $service->price,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
    
                $this->command->info("Relation créée : Booking ID {$booking->id}, Service ID {$service->id}");
            }
        }
    }
    
}
