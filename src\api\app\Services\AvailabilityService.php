<?php

namespace App\Services;

use App\Models\AvailabilityTemplate;
use App\Models\SlotException;
use App\Models\Hairdresser;
use App\Models\Salon;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AvailabilityService
{
    protected $timeSlotService;

    public function __construct(TimeSlotService $timeSlotService)
    {
        $this->timeSlotService = $timeSlotService;
    }

    /**
     * Créer ou mettre à jour un template de disponibilité
     */
    public function createOrUpdateTemplate(array $data)
    {
        return DB::transaction(function () use ($data) {
            $template = AvailabilityTemplate::updateOrCreate(
                [
                    'hairdresser_id' => $data['hairdresser_id'],
                    'salon_id' => $data['salon_id'],
                    'day_of_week' => $data['day_of_week']
                ],
                $data
            );

            // Régénérer les créneaux futurs pour ce jour de la semaine
            $this->regenerateFutureSlots($template);

            return $template;
        });
    }

    /**
     * <PERSON><PERSON>er des templates par défaut pour un coiffeur
     */
    public function createDefaultTemplates(Hairdresser $hairdresser, array $workingDays = [1, 2, 3, 4, 5])
    {
        $templates = [];
        
        foreach ($workingDays as $dayOfWeek) {
            $template = AvailabilityTemplate::create([
                'salon_id' => $hairdresser->salonId,
                'hairdresser_id' => $hairdresser->id,
                'name' => 'Horaires standard',
                'day_of_week' => $dayOfWeek,
                'start_time' => '09:00',
                'end_time' => '18:00',
                'slot_duration' => 30,
                'buffer_time' => 15,
                'max_advance_booking' => 30,
                'is_active' => true
            ]);
            
            $templates[] = $template;
        }

        // Générer les créneaux pour les 30 prochains jours
        $this->timeSlotService->generateTimeSlotsForHairdresser(
            $hairdresser,
            now()->toDateString(),
            now()->addDays(30)->toDateString()
        );

        return $templates;
    }

    /**
     * Créer une exception de disponibilité
     */
    public function createException(array $data)
    {
        return DB::transaction(function () use ($data) {
            $exception = SlotException::create($data);

            // Bloquer les créneaux affectés
            $this->blockAffectedSlots($exception);

            return $exception;
        });
    }

    /**
     * Créer une exception récurrente
     */
    public function createRecurringException(array $data)
    {
        return DB::transaction(function () use ($data) {
            $exception = SlotException::create($data);

            if ($exception->recurring) {
                $this->generateRecurringExceptions($exception);
            }

            return $exception;
        });
    }

    /**
     * Supprimer une exception et débloquer les créneaux
     */
    public function deleteException(SlotException $exception)
    {
        return DB::transaction(function () use ($exception) {
            // Débloquer les créneaux affectés
            $this->unblockAffectedSlots($exception);

            $exception->delete();

            return true;
        });
    }

    /**
     * Obtenir la disponibilité d'un coiffeur pour une période
     */
    public function getHairdresserAvailability(Hairdresser $hairdresser, $startDate, $endDate)
    {
        $availability = [];
        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        while ($current->lte($end)) {
            $dayOfWeek = $current->dayOfWeek;
            $dateString = $current->toDateString();

            // Récupérer le template pour ce jour
            $template = $hairdresser->getAvailabilityTemplateForDay($dayOfWeek);
            
            $dayAvailability = [
                'date' => $dateString,
                'day_of_week' => $dayOfWeek,
                'day_name' => $current->locale('fr')->dayName,
                'is_working_day' => !is_null($template),
                'template' => $template,
                'exceptions' => [],
                'available_slots' => [],
                'working_hours' => null
            ];

            if ($template) {
                $dayAvailability['working_hours'] = [
                    'start' => $template->start_time,
                    'end' => $template->end_time
                ];

                // Récupérer les exceptions pour cette date
                $exceptions = $hairdresser->getExceptionsForDate($dateString);
                $dayAvailability['exceptions'] = $exceptions;

                // Récupérer les créneaux disponibles
                $availableSlots = $hairdresser->getAvailableTimeSlots($dateString);
                $dayAvailability['available_slots'] = $availableSlots;
            }

            $availability[] = $dayAvailability;
            $current->addDay();
        }

        return $availability;
    }

    /**
     * Obtenir la disponibilité de tous les coiffeurs d'un salon
     */
    public function getSalonAvailability(Salon $salon, $startDate, $endDate)
    {
        $hairdressers = $salon->hairdressers()->active()->get();
        $salonAvailability = [];

        foreach ($hairdressers as $hairdresser) {
            $salonAvailability[$hairdresser->id] = [
                'hairdresser' => $hairdresser,
                'availability' => $this->getHairdresserAvailability($hairdresser, $startDate, $endDate)
            ];
        }

        return $salonAvailability;
    }

    /**
     * Vérifier si un coiffeur est disponible à une heure donnée
     */
    public function isHairdresserAvailable(Hairdresser $hairdresser, $dateTime, $duration = 30)
    {
        $date = Carbon::parse($dateTime)->toDateString();
        $time = Carbon::parse($dateTime)->format('H:i:s');
        
        return $hairdresser->isAvailableForTimeSlot($date, $time, 
            Carbon::parse($time)->addMinutes($duration)->format('H:i:s'));
    }

    /**
     * Trouver le prochain créneau disponible pour un coiffeur
     */
    public function findNextAvailableSlot(Hairdresser $hairdresser, $fromDateTime = null, $duration = 30)
    {
        $fromDate = $fromDateTime ? Carbon::parse($fromDateTime) : now();
        
        return $hairdresser->timeSlots()
            ->available()
            ->where('date', '>=', $fromDate->toDateString())
            ->where(function ($query) use ($fromDate) {
                $query->where('date', '>', $fromDate->toDateString())
                      ->orWhere(function ($q) use ($fromDate) {
                          $q->where('date', $fromDate->toDateString())
                            ->where('start_time', '>', $fromDate->format('H:i:s'));
                      });
            })
            ->where('duration', '>=', $duration)
            ->orderBy('date')
            ->orderBy('start_time')
            ->first();
    }

    /**
     * Régénérer les créneaux futurs pour un template
     */
    private function regenerateFutureSlots(AvailabilityTemplate $template)
    {
        $today = now()->toDateString();
        $endDate = now()->addDays($template->max_advance_booking)->toDateString();

        // Supprimer les créneaux futurs disponibles pour ce jour de la semaine
        $template->timeSlots()
            ->where('date', '>=', $today)
            ->where('status', 'available')
            ->delete();

        // Régénérer les créneaux
        $current = now();
        while ($current->lte(Carbon::parse($endDate))) {
            if ($current->dayOfWeek === $template->day_of_week) {
                $slots = $this->timeSlotService->generateSlotsForDay($template, $current->toDateString());
                if (!empty($slots)) {
                    \App\Models\TimeSlot::insert($slots);
                }
            }
            $current->addDay();
        }
    }

    /**
     * Bloquer les créneaux affectés par une exception
     */
    private function blockAffectedSlots(SlotException $exception)
    {
        $query = \App\Models\TimeSlot::where('hairdresser_id', $exception->hairdresser_id)
            ->where('date', $exception->date)
            ->where('status', 'available');

        if (!$exception->isAllDay()) {
            $query->where('start_time', '>=', $exception->start_time)
                  ->where('end_time', '<=', $exception->end_time);
        }

        $query->update([
            'status' => 'blocked',
            'notes' => 'Exception: ' . $exception->reason
        ]);
    }

    /**
     * Débloquer les créneaux affectés par une exception
     */
    private function unblockAffectedSlots(SlotException $exception)
    {
        $query = \App\Models\TimeSlot::where('hairdresser_id', $exception->hairdresser_id)
            ->where('date', $exception->date)
            ->where('status', 'blocked')
            ->where('notes', 'like', 'Exception: %');

        if (!$exception->isAllDay()) {
            $query->where('start_time', '>=', $exception->start_time)
                  ->where('end_time', '<=', $exception->end_time);
        }

        $query->update([
            'status' => 'available',
            'notes' => null
        ]);
    }

    /**
     * Générer les exceptions récurrentes
     */
    private function generateRecurringExceptions(SlotException $exception)
    {
        if (!$exception->recurring || !$exception->recurring_pattern) {
            return;
        }

        $endDate = $exception->recurring_until ?? now()->addYear()->toDateString();
        $dates = $exception->getRecurringDates(
            now()->toDateString(),
            $endDate
        );

        foreach ($dates as $date) {
            if ($date !== $exception->date->toDateString()) {
                SlotException::create([
                    'hairdresser_id' => $exception->hairdresser_id,
                    'date' => $date,
                    'start_time' => $exception->start_time,
                    'end_time' => $exception->end_time,
                    'type' => $exception->type,
                    'reason' => $exception->reason . ' (récurrent)',
                    'recurring' => false, // Les instances générées ne sont pas récurrentes
                    'is_active' => true
                ]);
            }
        }
    }

    /**
     * Copier les templates d'un coiffeur vers un autre
     */
    public function copyTemplates(Hairdresser $fromHairdresser, Hairdresser $toHairdresser)
    {
        $templates = $fromHairdresser->availabilityTemplates()->active()->get();
        $copiedTemplates = [];

        foreach ($templates as $template) {
            $newTemplate = $template->replicate();
            $newTemplate->hairdresser_id = $toHairdresser->id;
            $newTemplate->salon_id = $toHairdresser->salonId;
            $newTemplate->save();
            
            $copiedTemplates[] = $newTemplate;
        }

        // Générer les créneaux pour le nouveau coiffeur
        $this->timeSlotService->generateTimeSlotsForHairdresser(
            $toHairdresser,
            now()->toDateString(),
            now()->addDays(30)->toDateString()
        );

        return $copiedTemplates;
    }
}
