<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BookingService extends Model
{
    use HasFactory;

    protected $table = 'booking_services';

    protected $fillable = [
        'bookingId',
        'serviceId',
        'price_at_time',
    ];

    protected $casts = [
        'price_at_time' => 'decimal:2',
    ];

    public function booking()
    {
        return $this->belongsTo(Booking::class, 'bookingId');
    }

    public function service()
    {
        return $this->belongsTo(Service::class, 'serviceId');
    }
} 