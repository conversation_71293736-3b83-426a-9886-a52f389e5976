<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use App\Http\Resources\ServiceResource;

class ServiceManagementController extends Controller
{
    public function index(Request $request)
    {
        $query = Service::query();

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->salon_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        if ($request->has('price_range')) {
            $range = explode(',', $request->price_range);
            $query->whereBetween('price', $range);
        }

        $services = $query->paginate(10);
        return ServiceResource::collection($services);
    }

    public function serviceList()
    {
        return response()->json(Service::all());
    }


    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'salonId' => 'required|exists:salons,id',
            'category' => 'required|string',
        ]);

        $service = Service::create($validated);
        return new ServiceResource($service);
    }

    public function show(Service $service)
    {
        return new ServiceResource($service->load('salon'));
    }

    public function update(Request $request, Service $service)
    {
        // Validation des données de la requête
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'salonId' => 'required|exists:salons,id',
            'category' => 'required|string',
        ]);

        // Mise à jour du service avec les données validées
        $service->update($validated);

        // Retourne la ressource du service mis à jour
        return new ServiceResource($service);
    }


    public function destroy(Service $service)
    {
        // Check for future bookings with this service
        if ($service->bookings()->where('dateTime', '>', now())->exists()) {
            return response()->json([
                'message' => 'Cannot delete service with future bookings'
            ], 403);
        }

        $service->forceDelete();
        return response()->json(['message' => 'Service deleted successfully']);
    }

    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|exists:services,_id',
            'services.*.price' => 'required|numeric|min:0',
        ]);

        foreach ($validated['services'] as $serviceData) {
            Service::where('_id', $serviceData['id'])
                ->update(['price' => $serviceData['price']]);
        }

        return response()->json(['message' => 'Services updated successfully']);
    }

    public function getAnalytics(Request $request)
    {
        $analytics = [
            'most_booked' => Service::withCount('bookings')
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'highest_revenue' => Service::withSum('bookings', 'total_amount')
                ->orderBy('bookings_sum_total_amount', 'desc')
                ->take(5)
                ->get(),
            'average_duration' => Service::avg('duration'),
            'price_ranges' => Service::selectRaw('COUNT(*) as count, FLOOR(price/50)*50 as range')
                ->groupBy('range')
                ->get(),
        ];

        return response()->json($analytics);
    }
}
