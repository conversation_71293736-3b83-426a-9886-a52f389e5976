<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use App\Models\Booking;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ManagerDashboardController extends Controller
{
    public function getStats()
    {
        $salon = Auth::user()->managedSalon;

        $stats = [
            'daily_bookings' => Booking::where('salonId', $salon->id)
                ->whereDate('dateTime', today())
                ->count(),
            'total_staff' => User::where('salonId', $salon->id)
                ->where('role', 'hairdresser')
                ->count(),
            'today_revenue' => Booking::where('salonId', $salon->id)
                ->whereDate('dateTime', today())
                ->where('status', 'completed')
                ->sum('total_amount'),
            'pending_bookings' => Booking::where('salonId', $salon->id)
                ->where('status', 'pending')
                ->count(),
            'recent_bookings' => Booking::where('salonId', $salon->id)
                ->with(['client', 'hairdresser', 'services'])
                ->latest()
                ->take(5)
                ->get(),
            'staff_schedule' => $this->getStaffSchedule($salon->id),
            'service_stats' => $this->getServiceStats($salon->id),
        ];

        return response()->json($stats);
    }

    private function getStaffSchedule($salonId)
    {
        $hairdressers = User::where('salonId', $salonId)
            ->where('role', 'hairdresser')
            ->with(['bookings' => function ($query) {
                $query->whereDate('dateTime', today());
            }])
            ->get();

        return $hairdressers->map(function ($hairdresser) {
            return [
                'id' => $hairdresser->id,
                'name' => $hairdresser->firstName . ' ' . $hairdresser->lastName,
                'bookings' => $hairdresser->bookings->map(function ($booking) {
                    return [
                        'time' => $booking->dateTime,
                        'service' => $booking->services->first()->name,
                        'client' => $booking->client->firstName . ' ' . $booking->client->lastName,
                    ];
                }),
            ];
        });
    }

    private function getServiceStats($salonId)
    {
        return Service::where('salonId', $salonId)
            ->withCount(['bookings' => function ($query) {
                $query->whereDate('dateTime', today());
            }])
            ->orderBy('bookings_count', 'desc')
            ->take(5)
            ->get();
    }
}