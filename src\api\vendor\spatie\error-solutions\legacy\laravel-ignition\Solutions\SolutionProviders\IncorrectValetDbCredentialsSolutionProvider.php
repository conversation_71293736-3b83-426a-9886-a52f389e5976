<?php

namespace Spatie\LaravelIgnition\Solutions\SolutionProviders;

use Spatie\ErrorSolutions\SolutionProviders\Laravel\IncorrectValetDbCredentialsSolutionProvider as BaseIncorrectValetDbCredentialsSolutionProviderAlias;
use Spatie\Ignition\Contracts\HasSolutionsForThrowable;

class IncorrectValetDbCredentialsSolutionProvider extends BaseIncorrectValetDbCredentialsSolutionProviderAlias implements HasSolutionsForThrowable
{

}
