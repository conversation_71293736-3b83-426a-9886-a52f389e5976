<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Review;
use App\Models\User;
use App\Models\Salon;
use App\Models\Booking;
use Illuminate\Support\Facades\DB;

class ReviewSeeder extends Seeder
{
    public function run()
    {
        // Vérifiez les dépendances nécessaires
        $users = User::all();
        $salons = Salon::all();
        $bookings = Booking::all();

        if ($users->isEmpty() || $salons->isEmpty() || $bookings->isEmpty()) {
            $this->command->warn('Les tables users, salons ou bookings sont vides. Exécutez les seeders nécessaires.');
            return;
        }

        $this->command->info("Utilisateurs : {$users->count()}, Salons : {$salons->count()}, Réservations : {$bookings->count()}");

        // Génère des reviews pour 50 réservations
        $reviewsCreated = 0;

        foreach ($bookings->random(50) as $booking) {
            // Vérifie que la réservation a un utilisateur et un salon valides
            $user = $users->find($booking->clientId);
            $salon = $salons->find($booking->salonId);

            if (!$user || !$salon) {
                $this->command->warn("Réservation ID {$booking->id} : utilisateur ou salon introuvable.");
                continue;
            }

            // Crée un avis
            Review::create([
                'userId' => $user->id,
                'salonId' => $salon->id,
                'bookingId' => $booking->id,
                'rating' => rand(30, 50) / 10, // Génère une note aléatoire entre 3.0 et 5.0
                'comment' => fake()->sentence(rand(10, 20)), // Génère un commentaire aléatoire
                'source' => ['google', 'trustpilot', 'internal'][array_rand(['google', 'trustpilot', 'internal'])],
                'details' => json_encode([
                    'ip_address' => fake()->ipv4(),
                    'review_date' => fake()->dateTimeThisYear()->format('Y-m-d H:i:s'),
                ]),
                'verified' => (bool) rand(0, 1), // Statut vérifié aléatoire
            ]);

            $this->command->info("Avis créé : Booking ID {$booking->id}, User ID {$user->id}, Salon ID {$salon->id}");
            $reviewsCreated++;
        }

        // Journal de fin
        $this->command->info("Total de {$reviewsCreated} avis créés.");
    }
}
