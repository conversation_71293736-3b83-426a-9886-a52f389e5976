<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'clientId',
        'salonId',
        'hairdresserId',
        'dateTime',
        'status',
        'total_amount',
        'loyalty_points_earned',
        'rating',
        'cancellation_reason',
        'notes',
    ];

    protected $casts = [
        'id' => 'integer',
        'dateTime' => 'datetime',
        'total_amount' => 'decimal:2',
        'rating' => 'decimal:2',
        'loyalty_points_earned' => 'integer',
    ];

    public function client()
    {
        return $this->belongsTo(User::class, 'clientId');
    }

    // public function salon()
    // {
    //     return $this->belongsTo(Salon::class, 'salonId');
    // }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class, 'hairdresserId');
    }

    // public function services()
    // {
    //     return $this->belongsToMany(Service::class, 'booking_services')
    //         ->withPivot('price_at_time')
    //         ->withTimestamps();
    // }

    

    public function review()
    {
        return $this->hasOne(Review::class, 'bookingId');
    }

    public function calculateTotal()
    {
        $this->total_amount = $this->services->sum('pivot.price_at_time');
        $this->save();
    }

    public function canBeCancelled()
    {
        // Si la relation salon n'est pas chargée, la charger
        if (!$this->relationLoaded('salon')) {
            $this->load('salon');
        }
        
        // Si pas de salon, utiliser 24h par défaut
        $cancellationHours = $this->salon->cancellation_hours ?? 24;
        $cancellationDeadline = $this->dateTime->subHours($cancellationHours);
        
        return now()->lt($cancellationDeadline);
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'booking_services', 'bookingId', 'serviceId')
            ->withPivot('price_at_time')
            ->withTimestamps();
    }
}
