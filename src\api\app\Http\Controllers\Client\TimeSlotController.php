<?php

namespace App\Http\Controllers\Client;

use Carbon\Carbon;
use App\Models\Salon;
use App\Models\TimeSlot;
use App\Models\Hairdresser;
use Illuminate\Http\Request;
use App\Services\TimeSlotService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\AvailabilityService;
use App\Services\BookingValidationService;

class TimeSlotController extends Controller
{
    protected $timeSlotService;
    protected $availabilityService;
    protected $validationService;

    public function __construct(
        TimeSlotService $timeSlotService,
        AvailabilityService $availabilityService,
        BookingValidationService $validationService
    ) {
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
        $this->validationService = $validationService;
    }

    /**
     * Obtenir les créneaux disponibles pour un salon
     */
    public function getAvailableSlots(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'salon_id' => 'required|exists:salons,id',
            'hairdresser_id' => 'nullable|exists:hairdressers,id',
            'date' => 'nullable|date|after_or_equal:today',
            'duration' => 'nullable|integer|min:15|max:480',
            'services' => 'nullable|array',
            'services.*' => 'exists:services,id',
        ]);

        try {
            $slots = $this->timeSlotService->findAvailableSlots(
                $validated['salon_id'],
                $validated['hairdresser_id'] ?? null,
                $validated['date'] ?? null,
                $validated['duration'] ?? null,
                $validated['services'] ?? []
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'slots' => $slots,
                    'total' => $slots->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des créneaux',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir la disponibilité hebdomadaire
     */
    public function getWeeklyAvailability(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'salon_id' => 'required|exists:salons,id',
            'hairdresser_id' => 'nullable|exists:hairdressers,id',
            'start_date' => 'nullable|date',
            'duration' => 'nullable|integer|min:15|max:480',
        ]);

        try {
            $startDate = $validated['start_date'] ?? now()->startOfWeek()->toDateString();
            
            $weeklyData = $this->timeSlotService->getWeeklyAvailability(
                $validated['salon_id'],
                $startDate,
                $validated['hairdresser_id'] ?? null,
                $validated['duration'] ?? null
            );

            return response()->json([
                'success' => true,
                'data' => $weeklyData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération de la disponibilité hebdomadaire',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les coiffeurs disponibles avec leurs créneaux
     */
    public function getAvailableHairdressers(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'salon_id' => 'required|exists:salons,id',
            'date' => 'required|date|after_or_equal:today',
            'duration' => 'nullable|integer|min:15|max:480',
            'services' => 'nullable|array',
            'services.*' => 'exists:services,id',
        ]);

        try {
            $salon = Salon::find($validated['salon_id']);
            $duration = null;

            // Calculer la durée si des services sont fournis
            if (!empty($validated['services'])) {
                $duration = $this->timeSlotService->calculateTotalServiceDuration($validated['services']);
            } elseif (isset($validated['duration'])) {
                $duration = $validated['duration'];
            }

            $hairdressers = $salon->getHairdressersWithAvailability($validated['date'], $duration);

            return response()->json([
                'success' => true,
                'data' => [
                    'hairdressers' => $hairdressers,
                    'date' => $validated['date'],
                    'total_duration' => $duration
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des coiffeurs disponibles',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Vérifier la disponibilité d'un créneau spécifique
     */
    public function checkSlotAvailability(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'time_slot_id' => 'required|exists:time_slots,id',
            'services' => 'nullable|array',
            'services.*' => 'exists:services,id',
        ]);

        try {
            $timeSlot = TimeSlot::with(['hairdresser', 'salon'])->find($validated['time_slot_id']);

            if (!$timeSlot->canBeBooked()) {
                return response()->json([
                    'success' => false,
                    'available' => false,
                    'message' => 'Ce créneau n\'est plus disponible'
                ]);
            }

            // Vérifier si le créneau peut accueillir les services demandés
            if (!empty($validated['services'])) {
                $requiredDuration = $this->timeSlotService->calculateTotalServiceDuration($validated['services']);
                
                if (!$timeSlot->canAccommodateDuration($requiredDuration)) {
                    return response()->json([
                        'success' => false,
                        'available' => false,
                        'message' => 'La durée des services dépasse le créneau disponible',
                        'required_duration' => $requiredDuration,
                        'slot_duration' => $timeSlot->duration
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'available' => true,
                'data' => [
                    'time_slot' => $timeSlot,
                    'expires_at' => now()->addMinutes(15) // Réservation temporaire de 15 min
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification de disponibilité',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir des suggestions de créneaux alternatifs
     */
    public function getSuggestedAlternatives(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'salon_id' => 'required|exists:salons,id',
            'hairdresser_id' => 'required|exists:hairdressers,id',
            'requested_datetime' => 'required|date',
            'duration' => 'nullable|integer|min:15|max:480',
            'services' => 'nullable|array',
            'services.*' => 'exists:services,id',
        ]);

        try {
            $duration = $validated['duration'] ?? 60;
            
            if (!empty($validated['services'])) {
                $duration = $this->timeSlotService->calculateTotalServiceDuration($validated['services']);
            }

            $alternatives = $this->validationService->getSuggestedAlternatives(
                $validated['salon_id'],
                $validated['hairdresser_id'],
                $validated['requested_datetime'],
                $duration,
                $validated['services'] ?? []
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'alternatives' => $alternatives,
                    'requested_datetime' => $validated['requested_datetime'],
                    'duration' => $duration
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la recherche d\'alternatives',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les détails d'un créneau
     */
    public function getSlotDetails(TimeSlot $timeSlot): JsonResponse
    {
        try {
            $timeSlot->load(['hairdresser.user', 'salon', 'availabilityTemplate']);

            return response()->json([
                'success' => true,
                'data' => [
                    'time_slot' => $timeSlot,
                    'can_be_booked' => $timeSlot->canBeBooked(),
                    'formatted_time' => $timeSlot->getFormattedTimeRange(),
                    'duration_hours' => $timeSlot->getDurationInHours()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des détails du créneau',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculer la durée totale pour des services donnés
     */
    public function calculateServicesDuration(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'services' => 'required|array|min:1',
            'services.*' => 'exists:services,id',
        ]);

        try {
            $totalDuration = $this->timeSlotService->calculateTotalServiceDuration($validated['services']);

            return response()->json([
                'success' => true,
                'data' => [
                    'total_duration' => $totalDuration,
                    'total_hours' => round($totalDuration / 60, 2),
                    'services_count' => count($validated['services'])
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du calcul de la durée',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
