<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Manager\{
    ManagerDashboardController,
    Appointment<PERSON><PERSON>roller,
    StaffScheduleController,
    ServiceManagementController,
    CustomerManagementController,
    Staff<PERSON>ontroller,
    BookingManagementController
};

/*
|--------------------------------------------------------------------------
| Manager API Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['auth:sanctum', 'role:manager'])->group(function () {
    // Dashboard
    Route::get('/dashboard/stats', [ManagerDashboardController::class, 'getStats']);

    // Appointment Management
    Route::prefix('appointments')->group(function () {
        Route::get('/', [AppointmentController::class, 'index']);
        Route::get('/{booking}', [AppointmentController::class, 'show']);
        Route::put('/{booking}', [AppointmentController::class, 'update']);
        Route::get('/daily-schedule', [AppointmentController::class, 'getDailySchedule']);
        Route::post('/{booking}/reassign', [AppointmentController::class, 'reassignHairdresser']);
    });

    // Staff Schedule Management
    Route::prefix('staff-schedule')->group(function () {
        Route::get('/', [StaffScheduleController::class, 'index']);
        Route::put('/{staff}/availability', [StaffScheduleController::class, 'updateAvailability']);
        Route::get('/weekly', [StaffScheduleController::class, 'getWeeklySchedule']);
        Route::post('/{staff}/time-off', [StaffScheduleController::class, 'requestTimeOff']);
    });

    // Service Management
    Route::prefix('services')->group(function () {
        Route::get('/', [ServiceManagementController::class, 'index']);
        Route::get('/service-list', [ServiceManagementController::class, 'serviceList']);
        Route::post('/add-services', [ServiceManagementController::class, 'store']);
        Route::get('/{service}', [ServiceManagementController::class, 'show']);
        Route::put('/update-service/{service}', [ServiceManagementController::class, 'update']);
        Route::delete('/delete-service/{service}', [ServiceManagementController::class, 'destroy']);
        Route::post('/bulk-update', [ServiceManagementController::class, 'bulkUpdate']);
        Route::get('/analytics', [ServiceManagementController::class, 'getAnalytics']);
        Route::get('/stats', [ServiceManagementController::class, 'getServiceStats']);
    });

      // Staff Management
      Route::prefix('staff')->group(function () {
        Route::get('/', [StaffController::class, 'index']);
        Route::post('/', [StaffController::class, 'store']);
        Route::get('/{staff}', [StaffController::class, 'show']);
        Route::put('/{staff}', [StaffController::class, 'update']);
        Route::delete('/{staff}', [StaffController::class, 'destroy']);
        Route::put('/{staff}/availability', [StaffController::class, 'updateAvailability']);
    });


     // Booking Management
     Route::prefix('bookings')->group(function () {
        Route::get('/', [BookingManagementController::class, 'index']);
        Route::get('/test', [BookingManagementController::class, 'test']);
        Route::get('/{booking}', [BookingManagementController::class, 'show']);
        Route::put('/{booking}', [BookingManagementController::class, 'update']);
        Route::delete('/{booking}', [BookingManagementController::class, 'destroy']);
        Route::get('/analytics', [BookingManagementController::class, 'getAnalytics']);
        Route::post('/bulk-update', [BookingManagementController::class, 'bulkUpdate']);
        Route::post('/{booking}/confirm', [BookingManagementController::class, 'confirm']);
        Route::post('/{booking}/reject', [BookingManagementController::class, 'reject']);
        Route::post('/reset-statuses', [BookingManagementController::class, 'resetBookingStatuses']);
    });
    
    // Customer Management
    Route::prefix('customers')->group(function () {
        Route::get('/', [CustomerManagementController::class, 'index']);
        Route::get('/{customer}', [CustomerManagementController::class, 'show']);
        Route::put('/{customer}/preferences', [CustomerManagementController::class, 'updatePreferences']);
        Route::get('/analytics', [CustomerManagementController::class, 'getCustomerAnalytics']);
        Route::post('/promotional-message', [CustomerManagementController::class, 'sendPromotionalMessage']);
    });
});