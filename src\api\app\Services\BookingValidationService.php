<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\TimeSlot;
use App\Models\Service;
use App\Models\Hairdresser;
use App\Models\Salon;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class BookingValidationService
{
    protected $timeSlotService;
    protected $availabilityService;

    public function __construct(TimeSlotService $timeSlotService, AvailabilityService $availabilityService)
    {
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
    }

    /**
     * Valider une nouvelle réservation
     */
    public function validateNewBooking(array $bookingData)
    {
        $errors = [];

        // Validation de base
        $errors = array_merge($errors, $this->validateBasicData($bookingData));

        // Validation de la disponibilité
        $errors = array_merge($errors, $this->validateAvailability($bookingData));

        // Validation des services
        $errors = array_merge($errors, $this->validateServices($bookingData));

        // Validation des contraintes temporelles
        $errors = array_merge($errors, $this->validateTimeConstraints($bookingData));

        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

        return true;
    }

    /**
     * Valider la modification d'une réservation
     */
    public function validateBookingUpdate(Booking $booking, array $updateData)
    {
        $errors = [];

        // Vérifier si la réservation peut être modifiée
        if (!$this->canBookingBeModified($booking)) {
            $errors['booking'] = ['Cette réservation ne peut plus être modifiée'];
        }

        // Si on change l'heure ou la date
        if (isset($updateData['time_slot_id']) || isset($updateData['dateTime'])) {
            $errors = array_merge($errors, $this->validateAvailability($updateData, $booking->id));
        }

        // Si on change les services
        if (isset($updateData['services'])) {
            $errors = array_merge($errors, $this->validateServices($updateData));
        }

        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

        return true;
    }

    /**
     * Validation des données de base
     */
    private function validateBasicData(array $data)
    {
        $errors = [];

        // Vérifier que le salon existe et est actif
        if (isset($data['salonId'])) {
            $salon = Salon::find($data['salonId']);
            if (!$salon || $salon->status !== 'active') {
                $errors['salon'] = ['Le salon sélectionné n\'est pas disponible'];
            }
        }

        // Vérifier que le coiffeur existe et est actif
        if (isset($data['hairdresserId'])) {
            $hairdresser = Hairdresser::find($data['hairdresserId']);
            if (!$hairdresser || !$hairdresser->active) {
                $errors['hairdresser'] = ['Le coiffeur sélectionné n\'est pas disponible'];
            }

            // Vérifier que le coiffeur appartient au salon
            if (isset($data['salonId']) && $hairdresser && $hairdresser->salonId != $data['salonId']) {
                $errors['hairdresser'] = ['Le coiffeur ne travaille pas dans ce salon'];
            }
        }

        return $errors;
    }

    /**
     * Validation de la disponibilité
     */
    private function validateAvailability(array $data, $excludeBookingId = null)
    {
        $errors = [];

        if (isset($data['time_slot_id'])) {
            // Validation avec système de créneaux
            $timeSlot = TimeSlot::find($data['time_slot_id']);
            
            if (!$timeSlot) {
                $errors['time_slot'] = ['Le créneau sélectionné n\'existe pas'];
                return $errors;
            }

            if (!$timeSlot->canBeBooked()) {
                $errors['time_slot'] = ['Ce créneau n\'est plus disponible'];
            }

            // Vérifier la cohérence avec les autres données
            if (isset($data['hairdresserId']) && $timeSlot->hairdresser_id != $data['hairdresserId']) {
                $errors['time_slot'] = ['Le créneau ne correspond pas au coiffeur sélectionné'];
            }

            if (isset($data['salonId']) && $timeSlot->salon_id != $data['salonId']) {
                $errors['time_slot'] = ['Le créneau ne correspond pas au salon sélectionné'];
            }

        } elseif (isset($data['dateTime']) && isset($data['hairdresserId'])) {
            // Validation avec ancien système (rétrocompatibilité)
            $dateTime = Carbon::parse($data['dateTime']);
            $duration = $this->calculateBookingDuration($data);

            if (!$this->timeSlotService->checkAvailability(
                $data['hairdresserId'],
                $dateTime->toDateString(),
                $dateTime->format('H:i:s'),
                $duration,
                $excludeBookingId
            )) {
                $errors['dateTime'] = ['Ce créneau horaire n\'est pas disponible'];
            }
        }

        return $errors;
    }

    /**
     * Validation des services
     */
    private function validateServices(array $data)
    {
        $errors = [];

        if (!isset($data['services']) || empty($data['services'])) {
            $errors['services'] = ['Au moins un service doit être sélectionné'];
            return $errors;
        }

        $serviceIds = is_array($data['services']) ? $data['services'] : [$data['services']];
        $services = Service::whereIn('id', $serviceIds)->active()->get();

        if ($services->count() !== count($serviceIds)) {
            $errors['services'] = ['Un ou plusieurs services sélectionnés ne sont pas disponibles'];
        }

        // Vérifier que tous les services appartiennent au même salon
        if (isset($data['salonId'])) {
            $invalidServices = $services->where('salonId', '!=', $data['salonId']);
            if ($invalidServices->count() > 0) {
                $errors['services'] = ['Certains services ne sont pas disponibles dans ce salon'];
            }
        }

        // Vérifier la durée totale avec le créneau sélectionné
        if (isset($data['time_slot_id'])) {
            $timeSlot = TimeSlot::find($data['time_slot_id']);
            if ($timeSlot) {
                $totalDuration = $this->timeSlotService->calculateTotalServiceDuration($serviceIds);
                if (!$timeSlot->canAccommodateDuration($totalDuration)) {
                    $errors['services'] = ['La durée totale des services dépasse le créneau sélectionné'];
                }
            }
        }

        // Vérifier les contraintes de réservation à l'avance
        $this->validateAdvanceBookingConstraints($services, $data, $errors);

        return $errors;
    }

    /**
     * Validation des contraintes temporelles
     */
    private function validateTimeConstraints(array $data)
    {
        $errors = [];

        $bookingDateTime = null;

        if (isset($data['time_slot_id'])) {
            $timeSlot = TimeSlot::find($data['time_slot_id']);
            if ($timeSlot) {
                $bookingDateTime = $timeSlot->getFullDateTime();
            }
        } elseif (isset($data['dateTime'])) {
            $bookingDateTime = Carbon::parse($data['dateTime']);
        }

        if ($bookingDateTime) {
            // Vérifier que la réservation est dans le futur
            if ($bookingDateTime->isPast()) {
                $errors['dateTime'] = ['Impossible de réserver dans le passé'];
            }

            // Vérifier les heures d'ouverture du salon
            if (isset($data['salonId'])) {
                $salon = Salon::find($data['salonId']);
                if ($salon && !$this->isWithinSalonHours($salon, $bookingDateTime)) {
                    $errors['dateTime'] = ['Le créneau est en dehors des heures d\'ouverture du salon'];
                }
            }

            // Vérifier la limite de réservation à l'avance
            if (isset($data['hairdresserId'])) {
                $hairdresser = Hairdresser::find($data['hairdresserId']);
                if ($hairdresser) {
                    $template = $hairdresser->getAvailabilityTemplateForDay($bookingDateTime->dayOfWeek);
                    if ($template) {
                        $maxAdvanceDays = $template->max_advance_booking;
                        $maxDate = now()->addDays($maxAdvanceDays);
                        
                        if ($bookingDateTime->gt($maxDate)) {
                            $errors['dateTime'] = ["Impossible de réserver plus de {$maxAdvanceDays} jours à l'avance"];
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Vérifier si une réservation peut être modifiée
     */
    private function canBookingBeModified(Booking $booking)
    {
        // Vérifier le statut
        if (in_array($booking->status, ['completed', 'cancelled'])) {
            return false;
        }

        // Vérifier le délai de modification
        $startDateTime = $booking->getStartDateTime();
        if ($startDateTime && $startDateTime->isPast()) {
            return false;
        }

        // Vérifier les règles d'annulation du salon
        return $booking->canBeCancelledWithTimeSlots();
    }

    /**
     * Calculer la durée totale d'une réservation
     */
    private function calculateBookingDuration(array $data)
    {
        if (isset($data['services'])) {
            $serviceIds = is_array($data['services']) ? $data['services'] : [$data['services']];
            return $this->timeSlotService->calculateTotalServiceDuration($serviceIds);
        }

        return 60; // Durée par défaut
    }

    /**
     * Valider les contraintes de réservation à l'avance pour les services
     */
    private function validateAdvanceBookingConstraints($services, array $data, array &$errors)
    {
        $bookingDateTime = null;

        if (isset($data['time_slot_id'])) {
            $timeSlot = TimeSlot::find($data['time_slot_id']);
            if ($timeSlot) {
                $bookingDateTime = $timeSlot->getFullDateTime();
            }
        } elseif (isset($data['dateTime'])) {
            $bookingDateTime = Carbon::parse($data['dateTime']);
        }

        if ($bookingDateTime) {
            foreach ($services as $service) {
                // Vérifier le délai minimum de réservation
                if ($service->min_advance_booking) {
                    $minDateTime = now()->addMinutes($service->min_advance_booking);
                    if ($bookingDateTime->lt($minDateTime)) {
                        $hours = round($service->min_advance_booking / 60, 1);
                        $errors['services'][] = "Le service '{$service->name}' nécessite une réservation au moins {$hours}h à l'avance";
                    }
                }

                // Vérifier le délai maximum de réservation
                if ($service->max_advance_booking) {
                    $maxDateTime = now()->addMinutes($service->max_advance_booking);
                    if ($bookingDateTime->gt($maxDateTime)) {
                        $days = round($service->max_advance_booking / (60 * 24));
                        $errors['services'][] = "Le service '{$service->name}' ne peut être réservé plus de {$days} jours à l'avance";
                    }
                }

                // Vérifier les jours de disponibilité
                if ($service->available_days) {
                    $dayOfWeek = $bookingDateTime->dayOfWeek;
                    if (!in_array($dayOfWeek, $service->available_days)) {
                        $errors['services'][] = "Le service '{$service->name}' n'est pas disponible ce jour-là";
                    }
                }
            }
        }
    }

    /**
     * Vérifier si l'heure est dans les heures d'ouverture du salon
     */
    private function isWithinSalonHours(Salon $salon, Carbon $dateTime)
    {
        if (!$salon->hours) {
            return true; // Pas de restriction si pas d'heures définies
        }

        $dayOfWeek = $dateTime->dayOfWeek;
        $dayName = strtolower($dateTime->englishDayOfWeek);

        if (isset($salon->hours[$dayName])) {
            $hours = $salon->hours[$dayName];
            if (isset($hours['open']) && isset($hours['close'])) {
                $openTime = Carbon::parse($dateTime->toDateString() . ' ' . $hours['open']);
                $closeTime = Carbon::parse($dateTime->toDateString() . ' ' . $hours['close']);

                return $dateTime->between($openTime, $closeTime);
            }
        }

        return false;
    }

    /**
     * Obtenir les suggestions de créneaux alternatifs
     */
    public function getSuggestedAlternatives($salonId, $hairdresserId, $requestedDateTime, $duration, $serviceIds = [])
    {
        $requestedDate = Carbon::parse($requestedDateTime);
        
        // Chercher des alternatives dans les 7 jours suivants
        $alternatives = [];
        
        for ($i = 0; $i < 7; $i++) {
            $checkDate = $requestedDate->copy()->addDays($i);
            
            $slots = $this->timeSlotService->findAvailableSlots(
                $salonId,
                $hairdresserId,
                $checkDate->toDateString(),
                $duration,
                $serviceIds
            );

            if ($slots->count() > 0) {
                $alternatives[$checkDate->toDateString()] = $slots->take(3); // Max 3 suggestions par jour
            }

            if (count($alternatives) >= 3) { // Max 3 jours avec suggestions
                break;
            }
        }

        return $alternatives;
    }
}
