<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Services\TimeSlotService;
use App\Services\AvailabilityService;
use App\Models\Salon;
use App\Models\Hairdresser;
use App\Models\AvailabilityTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TimeSlotManagementController extends Controller
{
    protected $timeSlotService;
    protected $availabilityService;

    public function __construct(TimeSlotService $timeSlotService, AvailabilityService $availabilityService)
    {
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
    }

    /**
     * Obtenir tous les salons du propriétaire avec leurs statistiques
     */
    public function getAllSalonsOverview(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $salons = Auth::user()->ownedSalons;
            
            if ($salons->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun salon trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->toDateString();
            $endDate = $validated['end_date'] ?? now()->addDays(7)->toDateString();

            $salonsData = [];

            foreach ($salons as $salon) {
                $stats = $this->timeSlotService->getOccupancyStats($salon->id, $startDate, $endDate);
                $availability = $this->availabilityService->getSalonAvailability($salon, $startDate, $endDate);
                
                $salonsData[] = [
                    'salon' => $salon,
                    'stats' => $stats,
                    'hairdressers_count' => $salon->hairdressers()->active()->count(),
                    'availability_summary' => $this->summarizeAvailability($availability)
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'salons' => $salonsData,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération de la vue d\'ensemble',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configurer les templates par défaut pour un salon
     */
    public function setupDefaultTemplates(Request $request, Salon $salon): JsonResponse
    {
        $validated = $request->validate([
            'working_days' => 'required|array|min:1',
            'working_days.*' => 'integer|between:0,6',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'slot_duration' => 'required|integer|min:15|max:240',
            'buffer_time' => 'required|integer|min:0|max:60',
            'max_advance_booking' => 'required|integer|min:1|max:365',
        ]);

        try {
            // Vérifier que le salon appartient au propriétaire
            if (!Auth::user()->ownedSalons->contains($salon)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Salon non autorisé'
                ], 403);
            }

            $hairdressers = $salon->hairdressers()->active()->get();
            $totalTemplatesCreated = 0;

            foreach ($hairdressers as $hairdresser) {
                foreach ($validated['working_days'] as $dayOfWeek) {
                    $templateData = [
                        'hairdresser_id' => $hairdresser->id,
                        'salon_id' => $salon->id,
                        'name' => 'Horaires standard',
                        'day_of_week' => $dayOfWeek,
                        'start_time' => $validated['start_time'],
                        'end_time' => $validated['end_time'],
                        'slot_duration' => $validated['slot_duration'],
                        'buffer_time' => $validated['buffer_time'],
                        'max_advance_booking' => $validated['max_advance_booking'],
                        'is_active' => true
                    ];

                    $this->availabilityService->createOrUpdateTemplate($templateData);
                    $totalTemplatesCreated++;
                }
            }

            // Générer les créneaux pour les 30 prochains jours
            $generatedSlots = $this->timeSlotService->generateTimeSlotsForSalon(
                $salon,
                now()->toDateString(),
                now()->addDays(30)->toDateString()
            );

            return response()->json([
                'success' => true,
                'message' => 'Templates par défaut configurés avec succès',
                'data' => [
                    'templates_created' => $totalTemplatesCreated,
                    'slots_generated' => $generatedSlots,
                    'hairdressers_count' => $hairdressers->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la configuration des templates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques comparatives entre salons
     */
    public function getComparativeStats(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'metric' => 'nullable|in:occupancy_rate,total_bookings,revenue',
        ]);

        try {
            $salons = Auth::user()->ownedSalons;
            
            if ($salons->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun salon trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->startOfMonth()->toDateString();
            $endDate = $validated['end_date'] ?? now()->endOfMonth()->toDateString();
            $metric = $validated['metric'] ?? 'occupancy_rate';

            $comparativeData = [];
            $totalStats = [
                'total_slots' => 0,
                'total_booked' => 0,
                'total_available' => 0,
                'total_blocked' => 0,
                'average_occupancy' => 0
            ];

            foreach ($salons as $salon) {
                $stats = $this->timeSlotService->getOccupancyStats($salon->id, $startDate, $endDate);
                
                $comparativeData[] = [
                    'salon' => [
                        'id' => $salon->id,
                        'name' => $salon->name,
                        'address' => $salon->address
                    ],
                    'stats' => $stats,
                    'hairdressers_count' => $salon->hairdressers()->active()->count()
                ];

                $totalStats['total_slots'] += $stats['total'];
                $totalStats['total_booked'] += $stats['booked'];
                $totalStats['total_available'] += $stats['available'];
                $totalStats['total_blocked'] += $stats['blocked'];
            }

            $totalStats['average_occupancy'] = $totalStats['total_slots'] > 0 
                ? round(($totalStats['total_booked'] / $totalStats['total_slots']) * 100, 2) 
                : 0;

            // Trier par métrique choisie
            usort($comparativeData, function ($a, $b) use ($metric) {
                $valueA = $metric === 'occupancy_rate' ? $a['stats']['occupancy_rate'] : $a['stats'][$metric] ?? 0;
                $valueB = $metric === 'occupancy_rate' ? $b['stats']['occupancy_rate'] : $b['stats'][$metric] ?? 0;
                return $valueB <=> $valueA; // Tri décroissant
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'comparative_data' => $comparativeData,
                    'total_stats' => $totalStats,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ],
                    'sorted_by' => $metric
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques comparatives',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configurer les templates en masse pour plusieurs salons
     */
    public function bulkConfigureTemplates(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'salon_ids' => 'required|array|min:1',
            'salon_ids.*' => 'exists:salons,id',
            'template_config' => 'required|array',
            'template_config.working_days' => 'required|array|min:1',
            'template_config.working_days.*' => 'integer|between:0,6',
            'template_config.start_time' => 'required|date_format:H:i',
            'template_config.end_time' => 'required|date_format:H:i|after:template_config.start_time',
            'template_config.slot_duration' => 'required|integer|min:15|max:240',
            'template_config.buffer_time' => 'required|integer|min:0|max:60',
            'template_config.max_advance_booking' => 'required|integer|min:1|max:365',
        ]);

        try {
            $ownedSalonIds = Auth::user()->ownedSalons->pluck('id')->toArray();
            $requestedSalonIds = $validated['salon_ids'];
            
            // Vérifier que tous les salons appartiennent au propriétaire
            $unauthorizedSalons = array_diff($requestedSalonIds, $ownedSalonIds);
            if (!empty($unauthorizedSalons)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certains salons ne sont pas autorisés'
                ], 403);
            }

            $totalConfigured = 0;
            $results = [];

            foreach ($requestedSalonIds as $salonId) {
                $salon = Salon::find($salonId);
                $hairdressers = $salon->hairdressers()->active()->get();
                $salonConfigured = 0;

                foreach ($hairdressers as $hairdresser) {
                    foreach ($validated['template_config']['working_days'] as $dayOfWeek) {
                        $templateData = [
                            'hairdresser_id' => $hairdresser->id,
                            'salon_id' => $salon->id,
                            'name' => 'Configuration en masse',
                            'day_of_week' => $dayOfWeek,
                            'start_time' => $validated['template_config']['start_time'],
                            'end_time' => $validated['template_config']['end_time'],
                            'slot_duration' => $validated['template_config']['slot_duration'],
                            'buffer_time' => $validated['template_config']['buffer_time'],
                            'max_advance_booking' => $validated['template_config']['max_advance_booking'],
                            'is_active' => true
                        ];

                        $this->availabilityService->createOrUpdateTemplate($templateData);
                        $salonConfigured++;
                        $totalConfigured++;
                    }
                }

                $results[] = [
                    'salon' => $salon->name,
                    'templates_configured' => $salonConfigured,
                    'hairdressers_count' => $hairdressers->count()
                ];
            }

            return response()->json([
                'success' => true,
                'message' => "{$totalConfigured} templates configurés sur " . count($requestedSalonIds) . " salon(s)",
                'data' => [
                    'total_configured' => $totalConfigured,
                    'salons_results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la configuration en masse',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Résumer la disponibilité pour l'affichage
     */
    private function summarizeAvailability($availability)
    {
        $totalDays = count($availability);
        $workingDays = 0;
        $totalSlots = 0;
        $availableSlots = 0;

        foreach ($availability as $hairdresserId => $hairdresserData) {
            foreach ($hairdresserData['availability'] as $dayData) {
                if ($dayData['is_working_day']) {
                    $workingDays++;
                    $totalSlots += count($dayData['available_slots']);
                    $availableSlots += count($dayData['available_slots']);
                }
            }
        }

        return [
            'total_days' => $totalDays,
            'working_days' => $workingDays,
            'total_slots' => $totalSlots,
            'available_slots' => $availableSlots,
            'availability_rate' => $totalSlots > 0 ? round(($availableSlots / $totalSlots) * 100, 2) : 0
        ];
    }
}
