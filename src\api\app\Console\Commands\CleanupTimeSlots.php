<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TimeSlotService;
use Carbon\Carbon;

class CleanupTimeSlots extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'timeslots:cleanup 
                            {--days= : Supprimer les créneaux plus anciens que X jours (défaut: 30)}
                            {--dry-run : Mode test sans suppression}';

    /**
     * The console command description.
     */
    protected $description = 'Nettoyer les anciens créneaux horaires';

    protected $timeSlotService;

    public function __construct(TimeSlotService $timeSlotService)
    {
        parent::__construct();
        $this->timeSlotService = $timeSlotService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Nettoyage des anciens créneaux horaires');
        
        $days = $this->option('days') ?? 30;
        $dryRun = $this->option('dry-run');
        
        $cutoffDate = Carbon::now()->subDays($days)->toDateString();
        
        if ($dryRun) {
            $this->warn('⚠️  Mode test activé - Aucune suppression ne sera effectuée');
        }

        $this->info("Suppression des créneaux antérieurs au {$cutoffDate}...");

        try {
            if (!$dryRun) {
                $deletedCount = $this->timeSlotService->cleanupOldSlots($cutoffDate);
                $this->info("✅ {$deletedCount} créneaux supprimés");
            } else {
                $count = \App\Models\TimeSlot::where('date', '<', $cutoffDate)->count();
                $this->info("🔍 {$count} créneaux seraient supprimés");
            }

        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
