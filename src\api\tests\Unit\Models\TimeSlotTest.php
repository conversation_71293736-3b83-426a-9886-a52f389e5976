<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\TimeSlot;
use App\Models\AvailabilityTemplate;
use App\Models\Hairdresser;
use App\Models\Salon;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TimeSlotTest extends TestCase
{
    use RefreshDatabase;

    protected $salon;
    protected $hairdresser;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->salon = Salon::factory()->create();
        $this->hairdresser = Hairdresser::factory()->create(['salonId' => $this->salon->id]);
        $this->template = AvailabilityTemplate::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
        ]);
    }

    public function test_time_slot_can_be_created()
    {
        $timeSlot = TimeSlot::factory()->create([
            'availability_template_id' => $this->template->id,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $this->assertInstanceOf(TimeSlot::class, $timeSlot);
        $this->assertEquals($this->hairdresser->id, $timeSlot->hairdresser_id);
        $this->assertEquals($this->salon->id, $timeSlot->salon_id);
    }

    public function test_time_slot_relationships()
    {
        $timeSlot = TimeSlot::factory()->create([
            'availability_template_id' => $this->template->id,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $this->assertInstanceOf(AvailabilityTemplate::class, $timeSlot->availabilityTemplate);
        $this->assertInstanceOf(Hairdresser::class, $timeSlot->hairdresser);
        $this->assertInstanceOf(Salon::class, $timeSlot->salon);
    }

    public function test_time_slot_scopes()
    {
        // Créer des créneaux avec différents statuts
        $availableSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_AVAILABLE,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $bookedSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_BOOKED,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $blockedSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_BLOCKED,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        // Test des scopes
        $this->assertEquals(1, TimeSlot::available()->count());
        $this->assertEquals(1, TimeSlot::booked()->count());
        $this->assertEquals($availableSlot->id, TimeSlot::available()->first()->id);
        $this->assertEquals($bookedSlot->id, TimeSlot::booked()->first()->id);
    }

    public function test_time_slot_can_be_booked()
    {
        $timeSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $this->assertTrue($timeSlot->canBeBooked());

        // Créer une réservation
        $booking = Booking::factory()->create();
        $timeSlot->markAsBooked($booking);

        $this->assertFalse($timeSlot->fresh()->canBeBooked());
        $this->assertEquals(TimeSlot::STATUS_BOOKED, $timeSlot->fresh()->status);
        $this->assertEquals($booking->id, $timeSlot->fresh()->booking_id);
    }

    public function test_time_slot_can_be_blocked()
    {
        $timeSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_AVAILABLE,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $reason = 'Maintenance';
        $timeSlot->block($reason);

        $this->assertEquals(TimeSlot::STATUS_BLOCKED, $timeSlot->fresh()->status);
        $this->assertEquals($reason, $timeSlot->fresh()->notes);
        $this->assertNull($timeSlot->fresh()->booking_id);
    }

    public function test_time_slot_can_be_made_available()
    {
        $timeSlot = TimeSlot::factory()->create([
            'status' => TimeSlot::STATUS_BLOCKED,
            'notes' => 'Some reason',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $timeSlot->markAsAvailable();

        $this->assertEquals(TimeSlot::STATUS_AVAILABLE, $timeSlot->fresh()->status);
        $this->assertNull($timeSlot->fresh()->booking_id);
    }

    public function test_time_slot_date_time_methods()
    {
        $date = '2024-12-25';
        $startTime = '14:30:00';
        $endTime = '15:00:00';

        $timeSlot = TimeSlot::factory()->create([
            'date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $expectedStart = Carbon::parse($date . ' ' . $startTime);
        $expectedEnd = Carbon::parse($date . ' ' . $endTime);

        $this->assertEquals($expectedStart, $timeSlot->getFullDateTime());
        $this->assertEquals($expectedEnd, $timeSlot->getFullEndDateTime());
        $this->assertEquals('14:30 - 15:00', $timeSlot->getFormattedTimeRange());
    }

    public function test_time_slot_duration_methods()
    {
        $timeSlot = TimeSlot::factory()->create([
            'duration' => 90, // 90 minutes
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $this->assertEquals(1.5, $timeSlot->getDurationInHours());
        $this->assertTrue($timeSlot->canAccommodateDuration(60));
        $this->assertTrue($timeSlot->canAccommodateDuration(90));
        $this->assertFalse($timeSlot->canAccommodateDuration(120));
    }

    public function test_time_slot_conflict_detection()
    {
        $timeSlot = TimeSlot::factory()->create([
            'date' => '2024-12-25',
            'start_time' => '14:00:00',
            'end_time' => '15:00:00',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        // Test de conflit avec chevauchement
        $this->assertTrue($timeSlot->hasConflictWith('13:30:00', '14:30:00', '2024-12-25'));
        $this->assertTrue($timeSlot->hasConflictWith('14:30:00', '15:30:00', '2024-12-25'));
        $this->assertTrue($timeSlot->hasConflictWith('13:30:00', '15:30:00', '2024-12-25'));

        // Test sans conflit
        $this->assertFalse($timeSlot->hasConflictWith('13:00:00', '14:00:00', '2024-12-25'));
        $this->assertFalse($timeSlot->hasConflictWith('15:00:00', '16:00:00', '2024-12-25'));
        $this->assertFalse($timeSlot->hasConflictWith('14:00:00', '15:00:00', '2024-12-26'));
    }

    public function test_time_slot_past_future_detection()
    {
        $pastSlot = TimeSlot::factory()->create([
            'date' => now()->subDay()->toDateString(),
            'start_time' => '10:00:00',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $futureSlot = TimeSlot::factory()->create([
            'date' => now()->addDay()->toDateString(),
            'start_time' => '10:00:00',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $this->assertTrue($pastSlot->isPast());
        $this->assertFalse($pastSlot->isFuture());
        $this->assertFalse($futureSlot->isPast());
        $this->assertTrue($futureSlot->isFuture());
    }

    public function test_time_slot_upcoming_scope()
    {
        // Créer un créneau dans le passé
        TimeSlot::factory()->create([
            'date' => now()->subDay()->toDateString(),
            'start_time' => '10:00:00',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        // Créer un créneau futur
        $futureSlot = TimeSlot::factory()->create([
            'date' => now()->addDay()->toDateString(),
            'start_time' => '10:00:00',
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        // Créer un créneau aujourd'hui mais dans le passé
        TimeSlot::factory()->create([
            'date' => now()->toDateString(),
            'start_time' => now()->subHour()->format('H:i:s'),
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        // Créer un créneau aujourd'hui mais dans le futur
        $todayFutureSlot = TimeSlot::factory()->create([
            'date' => now()->toDateString(),
            'start_time' => now()->addHour()->format('H:i:s'),
            'hairdresser_id' => $this->hairdresser->id,
            'salon_id' => $this->salon->id,
        ]);

        $upcomingSlots = TimeSlot::upcoming()->get();
        
        $this->assertEquals(2, $upcomingSlots->count());
        $this->assertTrue($upcomingSlots->contains($futureSlot));
        $this->assertTrue($upcomingSlots->contains($todayFutureSlot));
    }
}
