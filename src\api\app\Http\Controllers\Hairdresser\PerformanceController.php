<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PerformanceController extends Controller
{
    public function getStats(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $stats = [
            'total_clients' => $this->getTotalClients(),
            'client_retention' => $this->getClientRetention(),
            'service_stats' => $this->getServiceStats($startDate, $endDate),
            'rating_stats' => $this->getRatingStats($startDate, $endDate),
            'revenue_stats' => $this->getRevenueStats($startDate, $endDate),
        ];

        return response()->json($stats);
    }

    private function getTotalClients()
    {
        return Booking::where('hairdresserId', Auth::id())
            ->distinct('clientId')
            ->count();
    }

    private function getClientRetention()
    {
        $totalClients = $this->getTotalClients();
        $repeatClients = Booking::where('hairdresserId', Auth::id())
            ->groupBy('clientId')
            ->havingRaw('COUNT(*) > 1')
            ->count();

        return [
            'total_clients' => $totalClients,
            'repeat_clients' => $repeatClients,
            'retention_rate' => $totalClients > 0 ? ($repeatClients / $totalClients) * 100 : 0,
        ];
    }

    private function getServiceStats($startDate, $endDate)
    {
        return Booking::where('hairdresserId', Auth::id())
            ->whereBetween('dateTime', [$startDate, $endDate])
            ->with('services')
            ->get()
            ->pluck('services')
            ->flatten()
            ->groupBy('name')
            ->map(function ($services) {
                return $services->count();
            });
    }

    private function getRatingStats($startDate, $endDate)
    {
        $ratings = Booking::where('hairdresserId', Auth::id())
            ->whereBetween('dateTime', [$startDate, $endDate])
            ->where('status', 'completed');

        return [
            'average_rating' => $ratings->avg('rating'),
            'total_ratings' => $ratings->count(),
            'rating_distribution' => $ratings
                ->selectRaw('rating, COUNT(*) as count')
                ->groupBy('rating')
                ->get(),
        ];
    }

    private function getRevenueStats($startDate, $endDate)
    {
        return [
            'total_revenue' => Booking::where('hairdresserId', Auth::id())
                ->whereBetween('dateTime', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('total_amount'),
            'daily_revenue' => Booking::where('hairdresserId', Auth::id())
                ->whereBetween('dateTime', [$startDate, $endDate])
                ->where('status', 'completed')
                ->selectRaw('DATE(dateTime) as date, SUM(total_amount) as total')
                ->groupBy('date')
                ->get(),
        ];
    }
}