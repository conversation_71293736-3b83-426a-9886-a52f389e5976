<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class StaffController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'hairdresser')
            ->whereIn('salonId', Auth::user()->salons()->pluck('id'));

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                  ->orWhere('lastName', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->salon_id);
        }

        $staff = $query->paginate(10);
        return UserResource::collection($staff);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'phone' => 'required|string',
            'salonId' => 'required|exists:salons,id',
            'specialties' => 'required|array',
            'availability' => 'required|array',
        ]);

        dd($validated);
        // $this->authorize('manage', Salon::find($validated['salonId']));

        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'hairdresser';


        $staff = User::create($validated);
        return new UserResource($staff);
    }

    public function update(Request $request, User $staff)
    {
        $this->authorize('manage', Salon::find($staff->salonId));

        $validated = $request->validate([
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $staff->id,
            'phone' => 'sometimes|string',
            'specialties' => 'sometimes|array',
            'availability' => 'sometimes|array',
            'status' => 'sometimes|in:active,inactive',
        ]);

        if ($request->has('password')) {
            $validated['password'] = Hash::make($request->password);
        }

        $staff->update($validated);
        return new UserResource($staff);
    }

    public function destroy(User $staff)
    {
        $this->authorize('manage', Salon::find($staff->salonId));

        if ($staff->bookings()->where('status', 'confirmed')->exists()) {
            return response()->json([
                'message' => 'Cannot delete staff member with active bookings'
            ], 403);
        }

        $staff->delete();
        return response()->json(['message' => 'Staff member deleted successfully']);
    }

    public function updateAvailability(Request $request, User $staff)
    {
        $this->authorize('manage', Salon::find($staff->salonId));

        $validated = $request->validate([
            'availability' => 'required|array',
            'availability.*' => 'array',
        ]);

        $staff->update(['availability' => $validated['availability']]);
        return new UserResource($staff);
    }
}