<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingConfirmation;
use App\Notifications\BookingCancellation;

class BookingController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'salonId' => 'required|exists:salons,_id',
            'hairdresserId' => 'required|exists:hairdressers,_id',
            'services' => 'required|array',
            'services.*' => 'exists:services,_id',
            'dateTime' => 'required|date|after:now',
        ]);

        $booking = Booking::create([
            'clientId' => auth()->id(),
            'salonId' => $validated['salonId'],
            'hairdresserId' => $validated['hairdresserId'],
            'dateTime' => $validated['dateTime'],
            'status' => 'confirmed',
        ]);

        $booking->services()->attach($validated['services']);

        // Send confirmation notification
        $booking->client->notify(new BookingConfirmation($booking));

        return new BookingResource($booking->load(['salon', 'hairdresser', 'services']));
    }

    public function cancel(Booking $booking)
    {
        $this->authorize('cancel', $booking);

        $booking->update(['status' => 'cancelled']);
        
        // Send cancellation notification
        $booking->client->notify(new BookingCancellation($booking));

        return response()->json(['message' => 'Booking cancelled successfully']);
    }

    public function reschedule(Request $request, Booking $booking)
    {
        $this->authorize('update', $booking);

        $validated = $request->validate([
            'dateTime' => 'required|date|after:now',
        ]);

        $booking->update(['dateTime' => $validated['dateTime']]);

        return new BookingResource($booking->load(['salon', 'hairdresser', 'services']));
    }
}