<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\BookingResource;
use App\Notifications\BookingStatusChanged;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $query = Booking::whereIn('salonId', Auth::user()->salons()->pluck('_id'))
            ->with(['client', 'hairdresser', 'services']);

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date')) {
            $query->whereDate('dateTime', $request->date);
        }

        if ($request->has('hairdresser_id')) {
            $query->where('hairdresserId', $request->hairdresser_id);
        }

        $bookings = $query->paginate(10);
        return BookingResource::collection($bookings);
    }

    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);
        return new BookingResource($booking->load(['client', 'hairdresser', 'services']));
    }

    public function update(Request $request, Booking $booking)
    {
        $this->authorize('manage', $booking);

        $validated = $request->validate([
            'status' => 'sometimes|in:confirmed,cancelled,completed,pending',
            'dateTime' => 'sometimes|date|after:now',
            'hairdresserId' => 'sometimes|exists:hairdressers,_id',
            'services' => 'sometimes|array',
            'services.*' => 'exists:services,_id',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);

        if ($booking->status !== $oldStatus) {
            $booking->client->notify(new BookingStatusChanged($booking));
        }

        return new BookingResource($booking);
    }

    public function getAnalytics(Request $request)
    {
        $salonIds = Auth::user()->salons()->pluck('_id');

        $analytics = [
            'total_bookings' => Booking::whereIn('salonId', $salonIds)->count(),
            'completed_bookings' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'completed')
                ->count(),
            'cancelled_bookings' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'cancelled')
                ->count(),
            'revenue' => Booking::whereIn('salonId', $salonIds)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'popular_time_slots' => Booking::whereIn('salonId', $salonIds)
                ->selectRaw('HOUR(dateTime) as hour, COUNT(*) as count')
                ->groupBy('hour')
                ->orderBy('count', 'desc')
                ->get(),
            'booking_trends' => Booking::whereIn('salonId', $salonIds)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', now()->subDays(30))
                ->groupBy('date')
                ->get(),
        ];

        return response()->json($analytics);
    }
}