<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HairdresserDashboardController extends Controller
{
    public function getStats()
    {
        $hairdresser = Auth::user();

        $stats = [
            'today_appointments' => Booking::where('hairdresserId', $hairdresser->id)
                ->whereDate('dateTime', today())
                ->count(),
            'total_clients' => Booking::where('hairdresserId', $hairdresser->id)
                ->distinct('clientId')
                ->count(),
            'completed_services' => Booking::where('hairdresserId', $hairdresser->id)
                ->where('status', 'completed')
                ->count(),
            'average_rating' => Booking::where('hairdresserId', $hairdresser->id)
                ->where('status', 'completed')
                ->avg('rating'),
            'upcoming_appointments' => $this->getUpcomingAppointments(),
            'performance_metrics' => $this->getPerformanceMetrics(),
        ];

        return response()->json($stats);
    }

    private function getUpcomingAppointments()
    {
        return Booking::where('hairdresserId', Auth::id())
            ->where('dateTime', '>', now())
            ->where('status', 'confirmed')
            ->with(['client', 'services'])
            ->orderBy('dateTime')
            ->take(5)
            ->get();
    }

    private function getPerformanceMetrics()
    {
        $startDate = now()->startOfMonth();
        $endDate = now()->endOfMonth();

        return [
            'monthly_bookings' => Booking::where('hairdresserId', Auth::id())
                ->whereBetween('dateTime', [$startDate, $endDate])
                ->count(),
            'monthly_revenue' => Booking::where('hairdresserId', Auth::id())
                ->whereBetween('dateTime', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('total_amount'),
            'client_satisfaction' => Booking::where('hairdresserId', Auth::id())
                ->where('status', 'completed')
                ->avg('rating'),
        ];
    }
}