<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Salon;
use App\Models\Hairdresser;
use App\Models\AvailabilityTemplate;
use App\Models\SlotException;
use App\Services\TimeSlotService;
use App\Services\AvailabilityService;
use Carbon\Carbon;

class TimeSlotSeeder extends Seeder
{
    protected $timeSlotService;
    protected $availabilityService;

    public function __construct()
    {
        $this->timeSlotService = app(TimeSlotService::class);
        $this->availabilityService = app(AvailabilityService::class);
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🕐 Initialisation du système de créneaux horaires...');

        // Créer des templates de disponibilité pour tous les coiffeurs
        $this->createAvailabilityTemplates();

        // Créer quelques exceptions d'exemple
        $this->createSampleExceptions();

        // Générer les créneaux pour les 30 prochains jours
        $this->generateTimeSlots();

        $this->command->info('✅ Système de créneaux horaires initialisé avec succès !');
    }

    /**
     * Créer des templates de disponibilité
     */
    private function createAvailabilityTemplates()
    {
        $this->command->info('📋 Création des templates de disponibilité...');

        $hairdressers = Hairdresser::all();
        $templatesCreated = 0;

        foreach ($hairdressers as $hairdresser) {
            // Templates pour lundi à vendredi (jours ouvrables)
            for ($dayOfWeek = 1; $dayOfWeek <= 5; $dayOfWeek++) {
                $template = AvailabilityTemplate::create([
                    'salon_id' => $hairdresser->salonId,
                    'hairdresser_id' => $hairdresser->id,
                    'name' => 'Horaires standard',
                    'day_of_week' => $dayOfWeek,
                    'start_time' => '09:00',
                    'end_time' => '18:00',
                    'slot_duration' => 30,
                    'buffer_time' => 15,
                    'max_advance_booking' => 30,
                    'is_active' => true,
                ]);
                $templatesCreated++;
            }

            // Template pour samedi (horaires réduits)
            AvailabilityTemplate::create([
                'salon_id' => $hairdresser->salonId,
                'hairdresser_id' => $hairdresser->id,
                'name' => 'Horaires samedi',
                'day_of_week' => 6,
                'start_time' => '09:00',
                'end_time' => '16:00',
                'slot_duration' => 30,
                'buffer_time' => 15,
                'max_advance_booking' => 30,
                'is_active' => true,
            ]);
            $templatesCreated++;
        }

        $this->command->info("✅ {$templatesCreated} templates créés");
    }

    /**
     * Créer des exceptions d'exemple
     */
    private function createSampleExceptions()
    {
        $this->command->info('🚫 Création d\'exceptions d\'exemple...');

        $hairdressers = Hairdresser::take(3)->get();
        $exceptionsCreated = 0;

        foreach ($hairdressers as $hairdresser) {
            // Pause déjeuner quotidienne
            SlotException::create([
                'hairdresser_id' => $hairdresser->id,
                'date' => now()->addDays(1)->toDateString(),
                'start_time' => '12:00',
                'end_time' => '13:00',
                'type' => 'lunch',
                'reason' => 'Pause déjeuner',
                'recurring' => true,
                'recurring_pattern' => [
                    'type' => 'weekly',
                    'days' => [1, 2, 3, 4, 5, 6] // Lundi à samedi
                ],
                'recurring_until' => now()->addMonths(3)->toDateString(),
                'is_active' => true,
            ]);
            $exceptionsCreated++;

            // Congé d'une journée
            SlotException::create([
                'hairdresser_id' => $hairdresser->id,
                'date' => now()->addDays(rand(7, 14))->toDateString(),
                'start_time' => null,
                'end_time' => null,
                'type' => 'vacation',
                'reason' => 'Congé personnel',
                'recurring' => false,
                'is_active' => true,
            ]);
            $exceptionsCreated++;

            // Formation (demi-journée)
            SlotException::create([
                'hairdresser_id' => $hairdresser->id,
                'date' => now()->addDays(rand(15, 21))->toDateString(),
                'start_time' => '14:00',
                'end_time' => '18:00',
                'type' => 'training',
                'reason' => 'Formation technique',
                'recurring' => false,
                'is_active' => true,
            ]);
            $exceptionsCreated++;
        }

        $this->command->info("✅ {$exceptionsCreated} exceptions créées");
    }

    /**
     * Générer les créneaux horaires
     */
    private function generateTimeSlots()
    {
        $this->command->info('🕐 Génération des créneaux horaires...');

        $salons = Salon::where('status', 'active')->get();
        $totalGenerated = 0;

        $startDate = now()->toDateString();
        $endDate = now()->addDays(30)->toDateString();

        foreach ($salons as $salon) {
            $generated = $this->timeSlotService->generateTimeSlotsForSalon($salon, $startDate, $endDate);
            $totalGenerated += $generated;
            $this->command->info("  - {$salon->name}: {$generated} créneaux");
        }

        $this->command->info("✅ {$totalGenerated} créneaux générés au total");
    }
}
