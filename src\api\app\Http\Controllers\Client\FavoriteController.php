<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use App\Models\User;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FavoriteController extends Controller
{
    public function index()
    {
        $favorites = [
            'salons' => Auth::user()->favoriteSalons()->get(),
            'hairdressers' => Auth::user()->favoriteHairdressers()->get(),
            'styles' => Auth::user()->favoriteStyles()->get(),
        ];

        return response()->json($favorites);
    }

    public function toggleSalon(Request $request, Salon $salon)
    {
        $user = Auth::user();
        
        if ($user->favoriteSalons()->where('salonId', $salon->id)->exists()) {
            $user->favoriteSalons()->detach($salon->id);
            $message = 'Salon removed from favorites';
        } else {
            $user->favoriteSalons()->attach($salon->id);
            $message = 'Salon added to favorites';
        }

        return response()->json(['message' => $message]);
    }

    public function toggleHairdresser(Request $request, User $hairdresser)
    {
        if ($hairdresser->role !== 'hairdresser') {
            return response()->json(['message' => 'Invalid hairdresser'], 422);
        }

        $user = Auth::user();
        
        if ($user->favoriteHairdressers()->where('hairdresserId', $hairdresser->id)->exists()) {
            $user->favoriteHairdressers()->detach($hairdresser->id);
            $message = 'Hairdresser removed from favorites';
        } else {
            $user->favoriteHairdressers()->attach($hairdresser->id);
            $message = 'Hairdresser added to favorites';
        }

        return response()->json(['message' => $message]);
    }

    public function toggleStyle(Request $request, Service $style)
    {
        $user = Auth::user();
        
        if ($user->favoriteStyles()->where('styleId', $style->id)->exists()) {
            $user->favoriteStyles()->detach($style->id);
            $message = 'Style removed from favorites';
        } else {
            $user->favoriteStyles()->attach($style->id);
            $message = 'Style added to favorites';
        }

        return response()->json(['message' => $message]);
    }
}