<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class BookingCancellation extends Notification
{
    use Queueable;

    protected $booking;

    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Booking Cancellation')
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('Your booking has been cancelled.')
            ->line('Cancelled appointment details:')
            ->line('Date: ' . $this->booking->dateTime->format('l, F j, Y'))
            ->line('Time: ' . $this->booking->dateTime->format('g:i A'))
            ->line('Salon: ' . $this->booking->salon->name)
            ->line('If you did not request this cancellation, please contact us immediately.')
            ->action('Book New Appointment', url('/salons/' . $this->booking->salonId))
            ->line('Thank you for your understanding.');
    }

    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_cancellation',
            'message' => 'Your booking has been cancelled',
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}