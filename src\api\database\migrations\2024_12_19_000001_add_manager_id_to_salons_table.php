<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('salons', function (Blueprint $table) {
            $table->foreignId('managerId')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('ownerId')->nullable()->constrained('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('salons', function (Blueprint $table) {
            $table->dropForeign(['managerId']);
            $table->dropForeign(['ownerId']);
            $table->dropColumn(['managerId', 'ownerId']);
        });
    }
}; 