<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Booking;

class BookingPolicy
{
    public function viewAny(User $user)
    {
        return true; // Permettre à tous les utilisateurs authentifiés de voir leurs réservations
    }

    public function view(User $user, Booking $booking)
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$booking->relationLoaded('hairdresser')) {
            $booking->load('hairdresser');
        }
        if (!$booking->relationLoaded('salon')) {
            $booking->load('salon');
        }

        return $user->id === $booking->clientId ||
               ($booking->hairdresser && $user->id === $booking->hairdresser->userId) ||
               ($booking->salon && $user->id === $booking->salon->ownerId) ||
               ($booking->salon && $user->id === $booking->salon->managerId);
    }

    public function create(User $user)
    {
        return true; // Permettre à tous les utilisateurs authentifiés de créer des réservations
    }

    public function update(User $user, Booking $booking)
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$booking->relationLoaded('hairdresser')) {
            $booking->load('hairdresser');
        }
        if (!$booking->relationLoaded('salon')) {
            $booking->load('salon');
        }

        return $user->id === $booking->clientId ||
               ($booking->hairdresser && $user->id === $booking->hairdresser->userId) ||
               ($booking->salon && $user->id === $booking->salon->managerId);
    }

    public function cancel(User $user, Booking $booking)
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$booking->relationLoaded('salon')) {
            $booking->load('salon');
        }

        if (!$booking->canBeCancelled()) {
            return false;
        }

        return $user->id === $booking->clientId ||
               ($booking->salon && $user->id === $booking->salon->managerId);
    }

    public function manage(User $user, Booking $booking)
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$booking->relationLoaded('salon')) {
            $booking->load('salon');
        }

        return ($booking->salon && $user->id === $booking->salon->ownerId) ||
               ($booking->salon && $user->id === $booking->salon->managerId);
    }

    
}