<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'email' => $this->email,
            'phone' => $this->phone,
            'gender' => $this->gender,
            'photoUrl' => $this->photoUrl,
            'role' => $this->role,
            'specialties' => $this->specialties,
            'availability' => $this->availability,
            'salonId' => $this->salonId, // Ajout du champ salonId
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
