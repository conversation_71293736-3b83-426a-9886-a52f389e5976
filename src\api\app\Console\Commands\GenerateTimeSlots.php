<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Salon;
use App\Models\Hairdresser;
use App\Services\TimeSlotService;
use Carbon\Carbon;

class GenerateTimeSlots extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'timeslots:generate 
                            {--salon= : ID du salon spécifique}
                            {--hairdresser= : ID du coiffeur spécifique}
                            {--days= : Nombre de jours à l\'avance (défaut: 7)}
                            {--force : Forcer la génération même si des créneaux existent}';

    /**
     * The console command description.
     */
    protected $description = 'Générer les créneaux horaires pour les jours à venir';

    protected $timeSlotService;

    public function __construct(TimeSlotService $timeSlotService)
    {
        parent::__construct();
        $this->timeSlotService = $timeSlotService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🕐 Génération des créneaux horaires');
        
        $salonId = $this->option('salon');
        $hairdresserId = $this->option('hairdresser');
        $days = $this->option('days') ?? 7;
        $force = $this->option('force');

        $startDate = now()->toDateString();
        $endDate = now()->addDays($days)->toDateString();

        try {
            $totalGenerated = 0;

            if ($hairdresserId) {
                $hairdresser = Hairdresser::find($hairdresserId);
                if (!$hairdresser) {
                    $this->error("Coiffeur avec ID {$hairdresserId} non trouvé");
                    return 1;
                }

                $generated = $this->generateForHairdresser($hairdresser, $startDate, $endDate, $force);
                $totalGenerated += $generated;

            } elseif ($salonId) {
                $salon = Salon::find($salonId);
                if (!$salon) {
                    $this->error("Salon avec ID {$salonId} non trouvé");
                    return 1;
                }

                $generated = $this->generateForSalon($salon, $startDate, $endDate, $force);
                $totalGenerated += $generated;

            } else {
                $salons = Salon::where('status', 'active')->get();
                
                foreach ($salons as $salon) {
                    $generated = $this->generateForSalon($salon, $startDate, $endDate, $force);
                    $totalGenerated += $generated;
                }
            }

            $this->info("✅ {$totalGenerated} créneaux générés avec succès");

        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function generateForHairdresser(Hairdresser $hairdresser, $startDate, $endDate, $force)
    {
        $this->line("Génération pour {$hairdresser->user->firstName} {$hairdresser->user->lastName}...");
        
        if (!$force) {
            $existingSlots = $hairdresser->timeSlots()
                ->whereBetween('date', [$startDate, $endDate])
                ->count();
            
            if ($existingSlots > 0) {
                $this->warn("  ⚠️  {$existingSlots} créneaux existants trouvés, ignoré (utilisez --force pour forcer)");
                return 0;
            }
        }

        $generated = $this->timeSlotService->generateTimeSlotsForHairdresser($hairdresser, $startDate, $endDate);
        $this->info("  ✅ {$generated} créneaux générés");
        
        return $generated;
    }

    private function generateForSalon(Salon $salon, $startDate, $endDate, $force)
    {
        $this->line("Génération pour le salon {$salon->name}...");
        
        if (!$force) {
            $existingSlots = $salon->timeSlots()
                ->whereBetween('date', [$startDate, $endDate])
                ->count();
            
            if ($existingSlots > 0) {
                $this->warn("  ⚠️  {$existingSlots} créneaux existants trouvés, ignoré (utilisez --force pour forcer)");
                return 0;
            }
        }

        $generated = $this->timeSlotService->generateTimeSlotsForSalon($salon, $startDate, $endDate);
        $this->info("  ✅ {$generated} créneaux générés");
        
        return $generated;
    }
}
