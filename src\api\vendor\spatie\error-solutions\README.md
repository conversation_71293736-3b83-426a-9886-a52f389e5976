# Error solutions

[![Latest Version on Packagist](https://img.shields.io/packagist/v/spatie/error-solutions.svg?style=flat-square)](https://packagist.org/packages/spatie/error-solutions)
[![Tests](https://img.shields.io/github/actions/workflow/status/spatie/error-solutions/run-tests.yml?branch=main&label=tests&style=flat-square)](https://github.com/spatie/error-solutions/actions/workflows/run-tests.yml)
[![Total Downloads](https://img.shields.io/packagist/dt/spatie/error-solutions.svg?style=flat-square)](https://packagist.org/packages/spatie/error-solutions)

At Spatie we develop multiple packages handling errors and providing solutions for these errors. This package is a collection of all these solutions.

## Support us

[<img src="https://github-ads.s3.eu-central-1.amazonaws.com/error-solutions.jpg?t=1" width="419px" />](https://spatie.be/github-ad-click/error-solutions)

We invest a lot of resources into creating [best in class open source packages](https://spatie.be/open-source). You can support us by [buying one of our paid products](https://spatie.be/open-source/support-us).

We highly appreciate you sending us a postcard from your hometown, mentioning which of our package(s) you are using. You'll find our address on [our contact page](https://spatie.be/about-us). We publish all received postcards on [our virtual postcard wall](https://spatie.be/open-source/postcards).

## Installation

You can install the package via composer:

```bash
composer require spatie/error-solutions
```

## Usage

We've got some excellent documentation on how to use solutions:

- [Flare](https://flareapp.io/docs/ignition/solutions/implementing-solutions)
- [Ignition](https://github.com/spatie/ignition/?tab=readme-ov-file#displaying-solutions)

## Testing

```bash
composer test
```

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Contributing

Please see [CONTRIBUTING](https://github.com/spatie/.github/blob/main/CONTRIBUTING.md) for details.

## Security Vulnerabilities

Please review [our security policy](../../security/policy) on how to report security vulnerabilities.

## Credits

- [Ruben Van Assche](https://github.com/rubenvanassche)
- [All Contributors](../../contributors)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
