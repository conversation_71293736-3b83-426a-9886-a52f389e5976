<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\SalonResource;
use App\Models\Service;
use Carbon\Carbon;

class SalonController extends Controller
{


    public function index()
    {
        $salons = Salon::all(); // Récupère tous les salons
        return response()->json([
            'success' => true,
            'data' => $salons
        ]);
    }

    /**
     * Recherche de salons par lieu, service et date
     */
    public function search(Request $request)
    {
        $query = Salon::query();

        // Recherche par lieu (nom du salon ou adresse)
        if ($request->has('lieu') && !empty($request->input('lieu'))) {
            $lieu = $request->input('lieu');
            $query->where(function($q) use ($lieu) {
                $q->where('name', 'like', "%{$lieu}%")
                  ->orWhere('address', 'like', "%{$lieu}%");
            });
        }

        // Recherche par service
        if ($request->has('service') && !empty($request->input('service'))) {
            $serviceId = $request->input('service');
            $query->whereHas('services', function($q) use ($serviceId) {
                $q->where('services.id', $serviceId);
            });
        }

        // Recherche par date d'ouverture
        if ($request->has('date') && !empty($request->input('date'))) {
            $date = Carbon::parse($request->input('date'));
            $dayOfWeek = $date->locale('fr')->dayName; // Obtenir le nom du jour en français
            
            $query->whereRaw("JSON_SEARCH(hours, 'one', ?, null, '$[*].day') IS NOT NULL", [$dayOfWeek]);
        }

        // Filtrer seulement les salons actifs
        $query->where('status', 'active');

        // Charger les relations nécessaires
        $query->with(['services', 'hairdressers', 'reviews']);

        $salons = $query->get();

        return response()->json([
            'success' => true,
            'data' => $salons,
            'filters' => [
                'lieu' => $request->input('lieu'),
                'service' => $request->input('service'),
                'date' => $request->input('date')
            ]
        ]);
    }

    public function show(Salon $salon)
    {
        return new SalonResource($salon->load(['services', 'hairdressers', 'reviews']));
    }

    public function services(Salon $salon)
    {
        return response()->json($salon->services);
    }

    public function hairdressers(Salon $salon)
    {
        return response()->json($salon->hairdressers);
    }

    public function reviews(Salon $salon)
    {
        return response()->json($salon->reviews()->with('user')->paginate(10));
    }

    /**
     * Liste tous les salons.
     */
    public function salonList()
    {
        $salons = Salon::all(); // Récupère tous les salons
        return response()->json([
            'success' => true,
            'data' => $salons
        ]);
    }

    public function showSalonDetails($id)
    {
        $salon = Salon::find($id);

        if (!$salon) {
            return response()->json(['message' => 'Salon not found'], 404);
        }

        return response()->json($salon);
    }

    public function showSalonWithServicesAndOtherDetails($idSalon)
    {
        $salon = Salon::with(['services', 'hairdressers.user', 'reviews'])->find($idSalon);

        if (!$salon) {
            return response()->json(['message' => 'Salon not found'], 404);
        }

        return response()->json($salon);
    }
}
