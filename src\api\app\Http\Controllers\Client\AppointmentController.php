<?php

namespace App\Http\Controllers\Client;

use App\Models\Salon;
use App\Models\Booking;
use App\Models\Service;
use Illuminate\Http\Request;
use App\Models\BookingService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\BookingResource;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        // $query = Booking::where('clientId', Auth::id())
        //     ->with(['salon', 'hairdresser', 'services']);


        $query = Booking::where('clientId', Auth::id())
            ->with(['salon', 'hairdresser', 'services']);

        // return  $query;

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date')) {
            $query->whereDate('dateTime', $request->date);
        }

        $bookings = $query->orderBy('dateTime', 'desc')->paginate(10);
        return BookingResource::collection($bookings);
    }

    public function store(Request $request)
    {
        // Support pour les deux systèmes : créneaux et dateTime legacy
        $rules = [
            'salonId' => 'required|exists:salons,id',
            'hairdresserId' => 'required|exists:users,id',
            'services' => 'required|array',
            'services.*' => 'exists:services,id',
        ];

        // Système de créneaux (prioritaire)
        if ($request->has('time_slot_id')) {
            $rules['time_slot_id'] = 'required|exists:time_slots,id';
        } else {
            // Système legacy
            $rules['dateTime'] = 'required|date|after:now';
        }

        $validated = $request->validate($rules);
        $validated['clientId'] = Auth::id();
        $validated['status'] = 'pending';

        try {
            // Utiliser le service de validation
            $validationService = app(\App\Services\BookingValidationService::class);
            $validationService->validateNewBooking($validated);

            // Vérifications legacy pour rétrocompatibilité
            if (!isset($validated['time_slot_id'])) {
                // Vérifier si le créneau horaire est déjà réservé pour ce coiffeur
                $existingBooking = Booking::where('hairdresserId', $validated['hairdresserId'])
                    ->where('dateTime', $validated['dateTime'])
                    ->whereIn('status', ['pending', 'confirmed'])
                    ->first();

                if ($existingBooking) {
                    return response()->json([
                        'message' => 'Ce créneau horaire est déjà réservé pour ce coiffeur. Veuillez choisir un autre horaire.',
                        'existing_booking' => [
                            'id' => $existingBooking->id,
                            'dateTime' => $existingBooking->dateTime,
                            'status' => $existingBooking->status
                        ]
                    ], 422);
                }
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        }

        // Vérifications spécifiques au système legacy
        if (!isset($validated['time_slot_id'])) {
            // Vérifier si le client a déjà une réservation à cette date/heure
            $clientExistingBooking = Booking::where('clientId', Auth::id())
                ->where('dateTime', $validated['dateTime'])
                ->whereIn('status', ['pending', 'confirmed'])
                ->first();

            if ($clientExistingBooking) {
                return response()->json([
                    'message' => 'Vous avez déjà une réservation à cette date et heure. Veuillez choisir un autre créneau.',
                    'existing_booking' => [
                        'id' => $clientExistingBooking->id,
                        'dateTime' => $clientExistingBooking->dateTime,
                        'status' => $clientExistingBooking->status
                    ]
                ], 422);
            }
        }

        // Calculate total amount and duration
        $services = Service::whereIn('id', $validated['services'])->get();
        $totalAmount = $services->sum('price');

        // Calculer la durée totale avec le nouveau système
        $timeSlotService = app(\App\Services\TimeSlotService::class);
        $totalDuration = $timeSlotService->calculateTotalServiceDuration($validated['services']);

        // Validate total amount doesn't exceed database limit
        if ($totalAmount > 999999.99) {
            return response()->json([
                'message' => 'Le montant total de la réservation est trop élevé. Veuillez sélectionner moins de services ou contacter le salon.',
                'total_amount' => $totalAmount
            ], 422);
        }

        $validated['total_amount'] = $totalAmount;
        $validated['total_duration'] = $totalDuration;

        // Calculer l'heure de fin
        if (isset($validated['time_slot_id'])) {
            $timeSlot = \App\Models\TimeSlot::find($validated['time_slot_id']);
            $validated['dateTime'] = $timeSlot->getFullDateTime();
            $validated['end_time'] = $timeSlot->getFullEndDateTime();
        } elseif (isset($validated['dateTime'])) {
            $validated['end_time'] = \Carbon\Carbon::parse($validated['dateTime'])->addMinutes($totalDuration);
        }

        try {
            \DB::transaction(function () use ($validated, $services, &$booking, $timeSlotService) {
                $booking = Booking::create($validated);

                // Assigner le créneau si système de créneaux
                if (isset($validated['time_slot_id'])) {
                    $timeSlot = \App\Models\TimeSlot::find($validated['time_slot_id']);
                    $timeSlotService->bookTimeSlot($timeSlot, $booking);
                }

                // Create booking_services records
                foreach ($services as $service) {
                    BookingService::create([
                        'bookingId' => $booking->id,
                        'serviceId' => $service->id,
                        'price_at_time' => $service->price,
                    ]);
                }

                // Add loyalty points (si la méthode existe)
                $user = Auth::user();
                if (method_exists($user, 'addLoyaltyPoints')) {
                    $user->addLoyaltyPoints($validated['total_amount'], 'Booking creation');
                }
            });

            return new BookingResource($booking->load(['salon', 'hairdresser', 'services', 'timeSlot']));

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erreur lors de la création de la réservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);
        return new BookingResource($booking->load(['salon', 'hairdresser', 'services']));
    }

    public function cancel(Booking $booking)
    {
        // Charger les relations nécessaires
        $booking->load(['salon', 'hairdresser']);

        // Vérifier que l'utilisateur est le client de cette réservation
        if ($booking->clientId !== Auth::id()) {
            return response()->json([
                'message' => 'Vous n\'êtes pas autorisé à annuler cette réservation'
            ], 403);
        }

        // Vérifier que la réservation peut être annulée
        if ($booking->status === 'cancelled') {
            return response()->json([
                'message' => 'Cette réservation est déjà annulée'
            ], 400);
        }

        if ($booking->status === 'completed') {
            return response()->json([
                'message' => 'Impossible d\'annuler une réservation terminée'
            ], 400);
        }

        // Check cancellation policy - TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
        $salon = $booking->salon;
        $cancellationHours = $salon->cancellation_hours ?? 24;

        if ($booking->dateTime->diffInHours(now()) < $cancellationHours) {
            return response()->json([
                'message' => "Impossible d'annuler une réservation moins de {$cancellationHours} heures avant le rendez-vous"
            ], 403);
        }

        $booking->update(['status' => 'cancelled']);

        // Refund loyalty points if applicable
        if ($booking->loyalty_points_earned) {
            Auth::user()->deductLoyaltyPoints($booking->loyalty_points_earned);
        }

        return response()->json(['message' => 'Réservation annulée avec succès']);
    }

    public function reschedule(Request $request, Booking $booking)
    {
        // Charger les relations nécessaires
        $booking->load(['salon', 'hairdresser']);

        // Vérifier que l'utilisateur est le client de cette réservation
        if ($booking->clientId !== Auth::id()) {
            return response()->json([
                'message' => 'Vous n\'êtes pas autorisé à reprogrammer cette réservation'
            ], 403);
        }

        // Vérifier que la réservation peut être reprogrammée
        if ($booking->status === 'cancelled') {
            return response()->json([
                'message' => 'Impossible de reprogrammer une réservation annulée'
            ], 400);
        }

        if ($booking->status === 'completed') {
            return response()->json([
                'message' => 'Impossible de reprogrammer une réservation terminée'
            ], 400);
        }

        $validated = $request->validate([
            'dateTime' => 'required|date|after:now',
        ], [
            'dateTime.required' => 'La nouvelle date et heure sont obligatoires',
            'dateTime.date' => 'Le format de date est invalide',
            'dateTime.after' => 'La nouvelle date doit être dans le futur',
        ]);

        // Vérifier si le nouveau créneau horaire est déjà réservé pour ce coiffeur
        $existingBooking = Booking::where('hairdresserId', $booking->hairdresserId)
            ->where('dateTime', $validated['dateTime'])
            ->where('id', '!=', $booking->id) // Exclure la réservation actuelle
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();

        if ($existingBooking) {
            return response()->json([
                'message' => 'Ce créneau horaire est déjà réservé pour ce coiffeur. Veuillez choisir un autre horaire.',
                'existing_booking' => [
                    'id' => $existingBooking->id,
                    'dateTime' => $existingBooking->dateTime,
                    'status' => $existingBooking->status
                ]
            ], 422);
        }

        // Vérifier si le client a déjà une autre réservation à cette date/heure
        $clientExistingBooking = Booking::where('clientId', Auth::id())
            ->where('dateTime', $validated['dateTime'])
            ->where('id', '!=', $booking->id) // Exclure la réservation actuelle
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();

        if ($clientExistingBooking) {
            return response()->json([
                'message' => 'Vous avez déjà une autre réservation à cette date et heure. Veuillez choisir un autre créneau.',
                'existing_booking' => [
                    'id' => $clientExistingBooking->id,
                    'dateTime' => $clientExistingBooking->dateTime,
                    'status' => $clientExistingBooking->status
                ]
            ], 422);
        }

        $booking->update($validated);

        return response()->json([
            'message' => 'Réservation reprogrammée avec succès',
            'booking' => new BookingResource($booking->load(['salon', 'hairdresser', 'services']))
        ]);
    }



    public function checkAvailability(Request $request)
    {
        $validated = $request->validate([
            'hairdresserId' => 'required|exists:users,id',
            'dateTime' => 'required|date|after:now',
        ], [
            'hairdresserId.required' => 'L\'identifiant du coiffeur est obligatoire',
            'hairdresserId.exists' => 'Le coiffeur sélectionné n\'existe pas',
            'dateTime.required' => 'La date et heure sont obligatoires',
            'dateTime.date' => 'Le format de date est invalide',
            'dateTime.after' => 'La date doit être dans le futur',
        ]);

        // Vérifier si le créneau horaire est déjà réservé pour ce coiffeur
        $existingBooking = Booking::where('hairdresserId', $validated['hairdresserId'])
            ->where('dateTime', $validated['dateTime'])
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();

        // Vérifier si le client a déjà une réservation à cette date/heure
        $clientExistingBooking = Booking::where('clientId', Auth::id())
            ->where('dateTime', $validated['dateTime'])
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();

        return response()->json([
            'available' => !$existingBooking && !$clientExistingBooking,
            'hairdresser_available' => !$existingBooking,
            'client_available' => !$clientExistingBooking,
            'existing_booking' => $existingBooking ? [
                'id' => $existingBooking->id,
                'dateTime' => $existingBooking->dateTime,
                'status' => $existingBooking->status
            ] : null,
            'client_conflict' => $clientExistingBooking ? [
                'id' => $clientExistingBooking->id,
                'dateTime' => $clientExistingBooking->dateTime,
                'status' => $clientExistingBooking->status
            ] : null,
        ]);
    }
}
