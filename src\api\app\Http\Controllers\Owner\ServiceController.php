<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\Salon;
use Illuminate\Http\Request;
use App\Http\Resources\ServiceResource;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $query = Service::whereIn('salonId', Auth::user()->salons()->pluck('_id'));

        if ($request->has('salon_id')) {
            $query->where('salonId', $request->salon_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        $services = $query->paginate(10);
        return ServiceResource::collection($services);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'salonId' => 'required|exists:salons,_id',
            'category' => 'required|string',
        ]);

        $this->authorize('manage', Salon::find($validated['salonId']));

        $service = Service::create($validated);
        return new ServiceResource($service);
    }

    public function update(Request $request, Service $service)
    {
        $this->authorize('manage', Salon::find($service->salonId));

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'duration' => 'sometimes|integer|min:1',
            'price' => 'sometimes|numeric|min:0',
            'category' => 'sometimes|string',
        ]);

        $service->update($validated);
        return new ServiceResource($service);
    }

    public function destroy(Service $service)
    {
        $this->authorize('manage', Salon::find($service->salonId));

        if ($service->bookings()->where('dateTime', '>', now())->exists()) {
            return response()->json([
                'message' => 'Cannot delete service with future bookings'
            ], 403);
        }

        $service->delete();
        return response()->json(['message' => 'Service deleted successfully']);
    }

    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|exists:services,_id',
            'services.*.price' => 'required|numeric|min:0',
        ]);

        foreach ($validated['services'] as $serviceData) {
            $service = Service::find($serviceData['id']);
            $this->authorize('manage', Salon::find($service->salonId));
            $service->update(['price' => $serviceData['price']]);
        }

        return response()->json(['message' => 'Services updated successfully']);
    }
}