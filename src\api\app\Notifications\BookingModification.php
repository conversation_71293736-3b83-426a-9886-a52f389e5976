<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class BookingModification extends Notification
{
    use Queueable;

    protected $booking;
    protected $changes;

    public function __construct(Booking $booking, array $changes)
    {
        $this->booking = $booking;
        $this->changes = $changes;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $message = (new MailMessage)
            ->subject('Booking Modification - ' . $this->booking->salon->name)
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('Your booking has been modified.');

        if (isset($this->changes['dateTime'])) {
            $message->line('New Date/Time: ' . $this->booking->dateTime->format('l, F j, Y g:i A'));
        }

        if (isset($this->changes['hairdresserId'])) {
            $message->line('New Hairdresser: ' . $this->booking->hairdresser->user->firstName . ' ' . $this->booking->hairdresser->user->lastName);
        }

        if (isset($this->changes['services'])) {
            $message->line('Updated Services: ' . $this->booking->services->pluck('name')->implode(', '));
            $message->line('New Total Amount: $' . number_format($this->booking->total_amount, 2));
        }

        return $message
            ->action('View Updated Booking', url('/bookings/' . $this->booking->id))
            ->line('If you did not request these changes, please contact us immediately.');
    }

    public function toArray($notifiable)
    {
        return [
            'booking_id' => $this->booking->id,
            'type' => 'booking_modification',
            'message' => 'Your booking has been modified',
            'changes' => $this->changes,
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}