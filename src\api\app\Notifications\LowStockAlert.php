<?php

namespace App\Notifications;

use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class LowStockAlert extends Notification
{
    use Queueable;

    protected $product;

    public function __construct(Product $product)
    {
        $this->product = $product;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Low Stock Alert - ' . $this->product->name)
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('This is to inform you that the following product is running low on stock:')
            ->line('Product: ' . $this->product->name)
            ->line('Current Stock: ' . $this->product->stock)
            ->line('Minimum Stock Level: ' . $this->product->min_stock)
            ->line('Supplier: ' . $this->product->supplier)
            ->action('Manage Inventory', url('/dashboard/inventory'))
            ->line('Please reorder soon to maintain adequate stock levels.');
    }

    public function toArray($notifiable)
    {
        return [
            'product_id' => $this->product->id,
            'type' => 'low_stock_alert',
            'message' => 'Low stock alert for ' . $this->product->name,
            'current_stock' => $this->product->stock,
            'min_stock' => $this->product->min_stock,
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}