<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition()
    {
        return [
            'name' => $this->faker->word,
            'category' => $this->faker->word,
            'stock' => $this->faker->numberBetween(0, 100),
            'min_stock' => $this->faker->numberBetween(0, 10),
            'price' => $this->faker->randomFloat(2, 1, 100),
            'supplier' => $this->faker->company,
            'salonId' => \App\Models\Salon::factory(),
        ];
    }
}
