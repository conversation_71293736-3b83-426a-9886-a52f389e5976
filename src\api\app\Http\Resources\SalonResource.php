<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalonResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'address' => $this->address,
            'hours' => $this->hours,
            'contact' => $this->contact,
            'images' => $this->images ? asset('storage/' . $this->images) : null, // URL complète de l'image
            'rating' => $this->rating,
            'services' => ServiceResource::collection($this->whenLoaded('services')),
            'hairdressers' => HairdresserResource::collection($this->whenLoaded('hairdressers')),
            'reviews' => ReviewResource::collection($this->whenLoaded('reviews')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
