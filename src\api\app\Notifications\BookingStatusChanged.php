<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Booking;

class BookingStatusChanged extends Notification
{
    use Queueable;

    protected $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $statusMessages = [
            'confirmed' => 'confirmé',
            'cancelled' => 'annulé',
            'completed' => 'terminé',
            'pending' => 'en attente'
        ];

        $status = $statusMessages[$this->booking->status] ?? $this->booking->status;

        return (new MailMessage)
            ->subject('Statut de votre réservation mis à jour')
            ->greeting('Bonjour ' . $notifiable->firstName . ' ' . $notifiable->lastName)
            ->line('Le statut de votre réservation a été mis à jour.')
            ->line('Détails de la réservation :')
            ->line('Salon : ' . $this->booking->salon->name)
            ->line('Date : ' . $this->booking->dateTime->format('d/m/Y à H:i'))
            ->line('Nouveau statut : ' . ucfirst($status))
            ->action('Voir mes réservations', url('/profile'))
            ->line('Merci de votre confiance !');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'status' => $this->booking->status,
            'salon_name' => $this->booking->salon->name,
            'date_time' => $this->booking->dateTime->format('Y-m-d H:i:s'),
            'message' => 'Le statut de votre réservation a été mis à jour vers : ' . $this->booking->status
        ];
    }
} 