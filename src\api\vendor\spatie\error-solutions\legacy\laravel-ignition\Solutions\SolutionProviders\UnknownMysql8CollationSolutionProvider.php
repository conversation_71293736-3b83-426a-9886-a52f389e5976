<?php

namespace Spatie\LaravelIgnition\Solutions\SolutionProviders;

use Spatie\ErrorSolutions\SolutionProviders\Laravel\UnknownMysql8CollationSolutionProvider as BaseUnknownMysql8CollationSolutionProviderAlias;
use Spatie\Ignition\Contracts\HasSolutionsForThrowable;

class UnknownMysql8CollationSolutionProvider extends BaseUnknownMysql8CollationSolutionProviderAlias implements HasSolutionsForThrowable
{

}
