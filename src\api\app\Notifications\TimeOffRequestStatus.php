<?php

namespace App\Notifications;

use App\Models\TimeOffRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class TimeOffRequestStatus extends Notification
{
    use Queueable;

    protected $timeOffRequest;

    public function __construct(TimeOffRequest $timeOffRequest)
    {
        $this->timeOffRequest = $timeOffRequest;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $status = ucfirst($this->timeOffRequest->status);
        $message = (new MailMessage)
            ->subject('Time Off Request ' . $status)
            ->greeting('Hello ' . $notifiable->firstName . '!')
            ->line('Your time off request has been ' . strtolower($status) . '.')
            ->line('Details:')
            ->line('Start Date: ' . $this->timeOffRequest->start_date->format('l, F j, Y'))
            ->line('End Date: ' . $this->timeOffRequest->end_date->format('l, F j, Y'));

        if ($this->timeOffRequest->response_note) {
            $message->line('Note: ' . $this->timeOffRequest->response_note);
        }

        return $message->action('View Details', url('/dashboard/availability'));
    }

    public function toArray($notifiable)
    {
        return [
            'time_off_request_id' => $this->timeOffRequest->id,
            'type' => 'time_off_request_status',
            'status' => $this->timeOffRequest->status,
            'message' => 'Your time off request has been ' . $this->timeOffRequest->status,
            'date' => now()->format('Y-m-d H:i:s'),
        ];
    }
}