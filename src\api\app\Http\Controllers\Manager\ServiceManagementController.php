<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use App\Http\Resources\ServiceResource;
use Illuminate\Support\Facades\Auth;

class ServiceManagementController extends Controller
{
    public function index(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour accéder aux services.'
            ], 403);
        }

        $query = Service::where('salonId', $salon->id);

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        $services = $query->paginate(10);
        return ServiceResource::collection($services);
    }

    public function store(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour créer des services.'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'duration' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string',
        ]);

        $validated['salonId'] = $salon->id;
        $service = Service::create($validated);

        return new ServiceResource($service);
    }

    public function update(Request $request, Service $service)
    {
        $user = Auth::user();
        
        // Pour les managers, vérifier qu'ils gèrent le salon du service
        if ($user->role === 'manager') {
            if ($user->salonId !== $service->salonId) {
                return response()->json(['message' => 'This action is unauthorized.'], 403);
            }
        } else {
            $this->authorize('manage', $service);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'duration' => 'sometimes|integer|min:1',
            'price' => 'sometimes|numeric|min:0',
            'category' => 'sometimes|string',
        ]);

        $service->update($validated);
        return new ServiceResource($service);
    }

    public function destroy(Service $service)
    {
        $user = Auth::user();
        
        // Pour les managers, vérifier qu'ils gèrent le salon du service
        if ($user->role === 'manager') {
            if ($user->salonId !== $service->salonId) {
                return response()->json(['message' => 'This action is unauthorized.'], 403);
            }
        } else {
            $this->authorize('manage', $service);
        }

        if ($service->bookings()->where('dateTime', '>', now())->exists()) {
            return response()->json([
                'message' => 'Cannot delete service with future bookings'
            ], 403);
        }

        $service->delete();
        return response()->json(['message' => 'Service deleted successfully']);
    }

    public function serviceList(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour accéder aux services.'
            ], 403);
        }

        $query = Service::where('salonId', $salon->id);

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        $services = $query->get();
        
        // Debug: retourner des informations sur le salon et les services
        return response()->json([
            'salon' => $salon,
            'services' => $services,
            'count' => $services->count(),
            'salonId' => $salon->id
        ]);
    }

    public function show(Service $service)
    {
        $salon = Auth::user()->salon;

        if (!$salon || $service->salonId !== $salon->id) {
            return response()->json([
                'message' => 'Accès non autorisé à ce service.'
            ], 403);
        }

        return new ServiceResource($service);
    }

    public function bulkUpdate(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour modifier les services.'
            ], 403);
        }

        $validated = $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|exists:services,id',
            'services.*.name' => 'sometimes|string|max:255',
            'services.*.description' => 'sometimes|string',
            'services.*.duration' => 'sometimes|integer|min:1',
            'services.*.price' => 'sometimes|numeric|min:0',
            'services.*.category' => 'sometimes|string',
            'services.*.active' => 'sometimes|boolean',
        ]);

        $updatedCount = 0;
        foreach ($validated['services'] as $serviceData) {
            $service = Service::find($serviceData['id']);
            
            // Vérifier que le service appartient au salon du manager
            if ($service && $service->salonId === $salon->id) {
                $service->update($serviceData);
                $updatedCount++;
            }
        }

        return response()->json([
            'message' => "{$updatedCount} services mis à jour avec succès."
        ]);
    }

    public function getAnalytics(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour accéder aux statistiques.'
            ], 403);
        }

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $stats = [
            'most_popular' => Service::where('salonId', $salon->id)
                ->withCount(['bookings' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('dateTime', [$startDate, $endDate]);
                }])
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'revenue_by_service' => Service::where('salonId', $salon->id)
                ->withSum(['bookings' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('dateTime', [$startDate, $endDate])
                          ->where('status', 'completed');
                }], 'total_amount')
                ->get(),
        ];

        return response()->json($stats);
    }

    public function getServiceStats(Request $request)
    {
        $salon = Auth::user()->salon;

        if (!$salon) {
            return response()->json([
                'message' => 'Vous devez être associé à un salon pour accéder aux statistiques.'
            ], 403);
        }

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $stats = [
            'most_popular' => Service::where('salonId', $salon->id)
                ->withCount(['bookings' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('dateTime', [$startDate, $endDate]);
                }])
                ->orderBy('bookings_count', 'desc')
                ->take(5)
                ->get(),
            'revenue_by_service' => Service::where('salonId', $salon->id)
                ->withSum(['bookings' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('dateTime', [$startDate, $endDate])
                          ->where('status', 'completed');
                }], 'total_amount')
                ->get(),
        ];

        return response()->json($stats);
    }
}