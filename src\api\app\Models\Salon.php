<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Salon extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'address',
        'hours',
        'contact',
        'images',
        'rating',
        'status',
        'cancellation_hours',
        'managerId',
        'ownerId',
    ];

    protected $casts = [
        'hours' => 'array',
        'contact' => 'array',
        'rating' => 'decimal:2',
    ];

    public function owner()
    {
        return $this->belongsTo(User::class, 'ownerId');
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'managerId');
    }

    public function hairdressers()
    {
        return $this->hasMany(Hairdresser::class, 'salonId');
    }

    public function services()
    {
        return $this->hasMany(Service::class, 'salonId');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'salonId');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'salonId');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'salonId');
    }

    public function clientPreferences()
    {
        return $this->hasMany(ClientPreference::class, 'salonId');
    }

    public function calculateRating()
    {
        $this->rating = $this->reviews()->avg('rating') ?? 0;
        $this->save();
    }

    
}