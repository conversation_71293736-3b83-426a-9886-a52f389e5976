<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Salon extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'address',
        'hours',
        'contact',
        'images',
        'rating',
        'status',
        'cancellation_hours',
        'managerId',
        'ownerId',
    ];

    protected $casts = [
        'hours' => 'array',
        'contact' => 'array',
        'rating' => 'decimal:2',
    ];

    public function owner()
    {
        return $this->belongsTo(User::class, 'ownerId');
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'managerId');
    }

    public function hairdressers()
    {
        return $this->hasMany(Hairdresser::class, 'salonId');
    }

    public function services()
    {
        return $this->hasMany(Service::class, 'salonId');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'salonId');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'salonId');
    }

    public function availabilityTemplates()
    {
        return $this->hasMany(AvailabilityTemplate::class, 'salon_id');
    }

    public function timeSlots()
    {
        return $this->hasMany(TimeSlot::class, 'salon_id');
    }

    /**
     * Méthodes pour le système de créneaux
     */
    public function getAvailableTimeSlots($date, $hairdresserId = null, $duration = null)
    {
        $query = $this->timeSlots()
            ->available()
            ->forDate($date)
            ->upcoming()
            ->with(['hairdresser'])
            ->orderBy('start_time');

        if ($hairdresserId) {
            $query->forHairdresser($hairdresserId);
        }

        if ($duration) {
            $query->where('duration', '>=', $duration);
        }

        return $query->get();
    }

    public function getHairdressersWithAvailability($date, $duration = null)
    {
        return $this->hairdressers()
            ->active()
            ->whereHas('timeSlots', function ($query) use ($date, $duration) {
                $query->available()
                      ->forDate($date)
                      ->upcoming();

                if ($duration) {
                    $query->where('duration', '>=', $duration);
                }
            })
            ->with(['timeSlots' => function ($query) use ($date, $duration) {
                $query->available()
                      ->forDate($date)
                      ->upcoming()
                      ->orderBy('start_time');

                if ($duration) {
                    $query->where('duration', '>=', $duration);
                }
            }])
            ->get();
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'salonId');
    }

    public function clientPreferences()
    {
        return $this->hasMany(ClientPreference::class, 'salonId');
    }

    public function calculateRating()
    {
        $this->rating = $this->reviews()->avg('rating') ?? 0;
        $this->save();
    }


}
