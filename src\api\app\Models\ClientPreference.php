<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'userId',
        'salonId',
        'preferred_hairdresser_id',
        'preferred_services',
        'preferred_days',
        'preferred_times',
        'notification_preferences',
        'notes',
    ];

    protected $casts = [
        'preferred_services' => 'array',
        'preferred_days' => 'array',
        'preferred_times' => 'array',
        'notification_preferences' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userId');
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function preferredHairdresser()
    {
        return $this->belongsTo(Hairdresser::class, 'preferred_hairdresser_id');
    }
}