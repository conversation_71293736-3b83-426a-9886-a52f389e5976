<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clientId');
            $table->unsignedBigInteger('salonId');
            $table->unsignedBigInteger('hairdresserId');
            $table->dateTime('dateTime');
            $table->enum('status', ['pending', 'confirmed', 'completed', 'cancelled'])->default('pending');
            $table->decimal('total_amount', 8, 2)->nullable();
            $table->integer('loyalty_points_earned')->nullable();
            $table->decimal('rating', 3, 2)->nullable();
            $table->string('cancellation_reason')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
        
        
    }

    public function down()
    {
        Schema::dropIfExists('bookings');
    }
};