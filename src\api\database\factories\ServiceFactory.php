<?php

namespace Database\Factories;

use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;

class ServiceFactory extends Factory
{
    protected $model = Service::class;

    public function definition()
    {
        return [
            'name' => $this->faker->word,
            'description' => $this->faker->paragraph,
            'duration' => $this->faker->numberBetween(15, 120),
            'price' => $this->faker->randomFloat(2, 10, 200),
            'salonId' => \App\Models\Salon::factory(),
            'category' => $this->faker->word,
        ];
    }
}
