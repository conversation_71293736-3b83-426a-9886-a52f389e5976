<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Services\TimeSlotService;
use App\Services\AvailabilityService;
use App\Models\Salon;
use App\Models\Hairdresser;
use App\Models\AvailabilityTemplate;
use App\Models\TimeSlot;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TimeSlotManagementController extends Controller
{
    protected $timeSlotService;
    protected $availabilityService;

    public function __construct(TimeSlotService $timeSlotService, AvailabilityService $availabilityService)
    {
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
    }

    /**
     * Obtenir la vue d'ensemble des créneaux du salon
     */
    public function getSalonOverview(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            
            if (!$salon) {
                return response()->json([
                    'success' => false,
                    'message' => 'Salon non trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->toDateString();
            $endDate = $validated['end_date'] ?? now()->addDays(7)->toDateString();

            $availability = $this->availabilityService->getSalonAvailability($salon, $startDate, $endDate);
            $stats = $this->timeSlotService->getOccupancyStats($salon->id, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => [
                    'salon' => $salon,
                    'availability' => $availability,
                    'stats' => $stats,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération de la vue d\'ensemble',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Générer les créneaux pour le salon
     */
    public function generateSlots(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'hairdresser_ids' => 'nullable|array',
            'hairdresser_ids.*' => 'exists:hairdressers,id',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            
            if (!$salon) {
                return response()->json([
                    'success' => false,
                    'message' => 'Salon non trouvé'
                ], 404);
            }

            $totalGenerated = 0;

            if (isset($validated['hairdresser_ids'])) {
                // Générer pour des coiffeurs spécifiques
                foreach ($validated['hairdresser_ids'] as $hairdresserId) {
                    $hairdresser = Hairdresser::where('id', $hairdresserId)
                        ->where('salonId', $salon->id)
                        ->first();
                    
                    if ($hairdresser) {
                        $generated = $this->timeSlotService->generateTimeSlotsForHairdresser(
                            $hairdresser,
                            $validated['start_date'],
                            $validated['end_date']
                        );
                        $totalGenerated += $generated;
                    }
                }
            } else {
                // Générer pour tout le salon
                $totalGenerated = $this->timeSlotService->generateTimeSlotsForSalon(
                    $salon,
                    $validated['start_date'],
                    $validated['end_date']
                );
            }

            return response()->json([
                'success' => true,
                'message' => "{$totalGenerated} créneaux générés avec succès",
                'data' => [
                    'generated_count' => $totalGenerated,
                    'period' => [
                        'start_date' => $validated['start_date'],
                        'end_date' => $validated['end_date']
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la génération des créneaux',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configurer les templates de disponibilité pour un coiffeur
     */
    public function configureHairdresserTemplates(Request $request, Hairdresser $hairdresser): JsonResponse
    {
        $validated = $request->validate([
            'templates' => 'required|array',
            'templates.*.day_of_week' => 'required|integer|between:0,6',
            'templates.*.start_time' => 'required|date_format:H:i',
            'templates.*.end_time' => 'required|date_format:H:i|after:templates.*.start_time',
            'templates.*.slot_duration' => 'required|integer|min:15|max:240',
            'templates.*.buffer_time' => 'required|integer|min:0|max:60',
            'templates.*.max_advance_booking' => 'required|integer|min:1|max:365',
            'templates.*.is_active' => 'boolean',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            
            if (!$salon || $hairdresser->salonId !== $salon->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Coiffeur non trouvé ou non autorisé'
                ], 404);
            }

            $createdTemplates = [];

            foreach ($validated['templates'] as $templateData) {
                $templateData['hairdresser_id'] = $hairdresser->id;
                $templateData['salon_id'] = $salon->id;
                
                $template = $this->availabilityService->createOrUpdateTemplate($templateData);
                $createdTemplates[] = $template;
            }

            return response()->json([
                'success' => true,
                'message' => 'Templates configurés avec succès',
                'data' => $createdTemplates
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la configuration des templates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bloquer des créneaux en masse
     */
    public function bulkBlockSlots(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'hairdresser_id' => 'required|exists:hairdressers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'reason' => 'required|string|max:255',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            $hairdresser = Hairdresser::where('id', $validated['hairdresser_id'])
                ->where('salonId', $salon->id)
                ->first();
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Coiffeur non trouvé'
                ], 404);
            }

            $blockedCount = $this->timeSlotService->blockTimeSlots(
                $validated['hairdresser_id'],
                $validated['start_date'],
                $validated['end_date'],
                $validated['start_time'] ?? null,
                $validated['end_time'] ?? null,
                $validated['reason']
            );

            return response()->json([
                'success' => true,
                'message' => "{$blockedCount} créneaux bloqués avec succès",
                'data' => [
                    'blocked_count' => $blockedCount,
                    'hairdresser' => $hairdresser->user->firstName . ' ' . $hairdresser->user->lastName
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du blocage des créneaux',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Débloquer des créneaux en masse
     */
    public function bulkUnblockSlots(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'hairdresser_id' => 'required|exists:hairdressers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            $hairdresser = Hairdresser::where('id', $validated['hairdresser_id'])
                ->where('salonId', $salon->id)
                ->first();
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Coiffeur non trouvé'
                ], 404);
            }

            $unblockedCount = $this->timeSlotService->unblockTimeSlots(
                $validated['hairdresser_id'],
                $validated['start_date'],
                $validated['end_date'],
                $validated['start_time'] ?? null,
                $validated['end_time'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => "{$unblockedCount} créneaux débloqués avec succès",
                'data' => [
                    'unblocked_count' => $unblockedCount,
                    'hairdresser' => $hairdresser->user->firstName . ' ' . $hairdresser->user->lastName
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du déblocage des créneaux',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Copier les templates d'un coiffeur vers d'autres
     */
    public function copyTemplates(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'source_hairdresser_id' => 'required|exists:hairdressers,id',
            'target_hairdresser_ids' => 'required|array|min:1',
            'target_hairdresser_ids.*' => 'exists:hairdressers,id',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            
            $sourceHairdresser = Hairdresser::where('id', $validated['source_hairdresser_id'])
                ->where('salonId', $salon->id)
                ->first();
            
            if (!$sourceHairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Coiffeur source non trouvé'
                ], 404);
            }

            $copiedCount = 0;

            foreach ($validated['target_hairdresser_ids'] as $targetId) {
                $targetHairdresser = Hairdresser::where('id', $targetId)
                    ->where('salonId', $salon->id)
                    ->first();
                
                if ($targetHairdresser && $targetId !== $validated['source_hairdresser_id']) {
                    $this->availabilityService->copyTemplates($sourceHairdresser, $targetHairdresser);
                    $copiedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Templates copiés vers {$copiedCount} coiffeur(s)",
                'data' => [
                    'copied_count' => $copiedCount,
                    'source' => $sourceHairdresser->user->firstName . ' ' . $sourceHairdresser->user->lastName
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la copie des templates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques détaillées du salon
     */
    public function getDetailedStats(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month,hairdresser',
        ]);

        try {
            $salon = Auth::user()->managedSalon;
            
            if (!$salon) {
                return response()->json([
                    'success' => false,
                    'message' => 'Salon non trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->startOfMonth()->toDateString();
            $endDate = $validated['end_date'] ?? now()->endOfMonth()->toDateString();
            $groupBy = $validated['group_by'] ?? 'day';

            // Statistiques globales
            $globalStats = $this->timeSlotService->getOccupancyStats($salon->id, $startDate, $endDate);

            // Statistiques par coiffeur
            $hairdresserStats = [];
            $hairdressers = $salon->hairdressers()->active()->get();
            
            foreach ($hairdressers as $hairdresser) {
                $stats = $this->timeSlotService->getOccupancyStats(
                    $salon->id,
                    $startDate,
                    $endDate,
                    $hairdresser->id
                );
                
                $hairdresserStats[] = [
                    'hairdresser' => $hairdresser,
                    'stats' => $stats
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'global_stats' => $globalStats,
                    'hairdresser_stats' => $hairdresserStats,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ],
                    'group_by' => $groupBy
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Nettoyer les anciens créneaux
     */
    public function cleanupOldSlots(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'before_date' => 'nullable|date|before:today',
        ]);

        try {
            $beforeDate = $validated['before_date'] ?? Carbon::now()->subDays(30)->toDateString();
            $deletedCount = $this->timeSlotService->cleanupOldSlots($beforeDate);

            return response()->json([
                'success' => true,
                'message' => "{$deletedCount} anciens créneaux supprimés",
                'data' => [
                    'deleted_count' => $deletedCount,
                    'before_date' => $beforeDate
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du nettoyage',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
