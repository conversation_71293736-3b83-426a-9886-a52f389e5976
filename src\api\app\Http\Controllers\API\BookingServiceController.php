<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\BookingService;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookingServiceController extends Controller
{
    /**
     * Obtenir les services d'une réservation
     */
    public function getBookingServices(Booking $booking)
    {
        $services = $booking->services()->withPivot('price_at_time')->get();
        
        return response()->json([
            'success' => true,
            'data' => $services->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'description' => $service->description,
                    'duration' => $service->duration,
                    'price' => $service->price,
                    'price_at_time' => $service->pivot->price_at_time,
                ];
            })
        ]);
    }

    /**
     * Ajouter des services à une réservation
     */
    public function addServices(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'services' => 'required|array',
            'services.*.serviceId' => 'required|exists:services,id',
            'services.*.price_at_time' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            foreach ($validated['services'] as $serviceData) {
                BookingService::create([
                    'bookingId' => $booking->id,
                    'serviceId' => $serviceData['serviceId'],
                    'price_at_time' => $serviceData['price_at_time'],
                ]);
            }

            // Recalculer le montant total
            $booking->calculateTotal();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Services ajoutés avec succès',
                'data' => $booking->load('services')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout des services',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer un service d'une réservation
     */
    public function removeService(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'serviceId' => 'required|exists:services,id',
        ]);

        try {
            DB::beginTransaction();

            BookingService::where('bookingId', $booking->id)
                ->where('serviceId', $validated['serviceId'])
                ->delete();

            // Recalculer le montant total
            $booking->calculateTotal();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Service supprimé avec succès',
                'data' => $booking->load('services')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression du service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour le prix d'un service dans une réservation
     */
    public function updateServicePrice(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'serviceId' => 'required|exists:services,id',
            'price_at_time' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            BookingService::where('bookingId', $booking->id)
                ->where('serviceId', $validated['serviceId'])
                ->update(['price_at_time' => $validated['price_at_time']]);

            // Recalculer le montant total
            $booking->calculateTotal();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Prix mis à jour avec succès',
                'data' => $booking->load('services')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour du prix',
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 