<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Salon;
use App\Models\Hairdresser;
use App\Models\TimeSlot;
use App\Models\AvailabilityTemplate;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class TimeSlotApiTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $salon;
    protected $hairdresser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->salon = Salon::factory()->create();
        $this->hairdresser = Hairdresser::factory()->create(['salonId' => $this->salon->id]);
        
        Sanctum::actingAs($this->user);
    }

    public function test_get_available_slots()
    {
        // Créer des créneaux disponibles
        TimeSlot::factory()->count(3)->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
        ]);

        $response = $this->getJson('/api/client/time-slots/available?' . http_build_query([
            'salon_id' => $this->salon->id,
        ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'slots',
                        'total'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'total' => 3
                    ]
                ]);
    }

    public function test_get_available_slots_with_hairdresser_filter()
    {
        // Créer un autre coiffeur
        $otherHairdresser = Hairdresser::factory()->create(['salonId' => $this->salon->id]);

        // Créer des créneaux pour les deux coiffeurs
        TimeSlot::factory()->count(2)->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
        ]);

        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $otherHairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
        ]);

        $response = $this->getJson('/api/client/time-slots/available?' . http_build_query([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'total' => 2
                    ]
                ]);
    }

    public function test_get_available_slots_with_duration_filter()
    {
        // Créer des créneaux avec différentes durées
        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 30,
        ]);

        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 90,
        ]);

        $response = $this->getJson('/api/client/time-slots/available?' . http_build_query([
            'salon_id' => $this->salon->id,
            'duration' => 60,
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'total' => 1 // Seul le créneau de 90 min peut accueillir 60 min
                    ]
                ]);
    }

    public function test_get_weekly_availability()
    {
        $startDate = now()->startOfWeek()->toDateString();

        // Créer des créneaux pour différents jours
        TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => $startDate,
        ]);

        $response = $this->getJson('/api/client/time-slots/weekly?' . http_build_query([
            'salon_id' => $this->salon->id,
            'start_date' => $startDate,
        ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'date',
                            'day_name',
                            'slots'
                        ]
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);

        // Vérifier qu'on a 7 jours
        $this->assertCount(7, $response->json('data'));
    }

    public function test_get_available_hairdressers()
    {
        $date = now()->addDay()->toDateString();

        // Créer des créneaux pour le coiffeur
        TimeSlot::factory()->count(2)->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => $date,
        ]);

        $response = $this->getJson('/api/client/time-slots/hairdressers?' . http_build_query([
            'salon_id' => $this->salon->id,
            'date' => $date,
        ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'hairdressers',
                        'date',
                        'total_duration'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'date' => $date
                    ]
                ]);

        $hairdressers = $response->json('data.hairdressers');
        $this->assertCount(1, $hairdressers);
        $this->assertEquals($this->hairdresser->id, $hairdressers[0]['id']);
    }

    public function test_check_slot_availability()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 60,
        ]);

        $response = $this->postJson('/api/client/time-slots/check-availability', [
            'time_slot_id' => $timeSlot->id,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'available' => true
                ]);
    }

    public function test_check_slot_availability_with_services()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 60,
        ]);

        $service = Service::factory()->create([
            'duration' => 30,
            'preparation_time' => 5,
            'cleanup_time' => 5,
        ]);

        $response = $this->postJson('/api/client/time-slots/check-availability', [
            'time_slot_id' => $timeSlot->id,
            'services' => [$service->id],
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'available' => true
                ]);
    }

    public function test_check_slot_availability_insufficient_duration()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'duration' => 30,
        ]);

        $service = Service::factory()->create([
            'duration' => 60,
            'preparation_time' => 10,
            'cleanup_time' => 10,
        ]);

        $response = $this->postJson('/api/client/time-slots/check-availability', [
            'time_slot_id' => $timeSlot->id,
            'services' => [$service->id],
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => false,
                    'available' => false
                ])
                ->assertJsonFragment([
                    'message' => 'La durée des services dépasse le créneau disponible'
                ]);
    }

    public function test_get_slot_details()
    {
        $timeSlot = TimeSlot::factory()->create([
            'salon_id' => $this->salon->id,
            'hairdresser_id' => $this->hairdresser->id,
            'status' => TimeSlot::STATUS_AVAILABLE,
            'date' => now()->addDay()->toDateString(),
            'start_time' => '14:00:00',
            'end_time' => '15:00:00',
            'duration' => 60,
        ]);

        $response = $this->getJson("/api/client/time-slots/{$timeSlot->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'time_slot',
                        'can_be_booked',
                        'formatted_time',
                        'duration_hours'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'can_be_booked' => true,
                        'formatted_time' => '14:00 - 15:00',
                        'duration_hours' => 1.0
                    ]
                ]);
    }

    public function test_calculate_services_duration()
    {
        $service1 = Service::factory()->create([
            'duration' => 30,
            'preparation_time' => 5,
            'cleanup_time' => 5,
        ]);

        $service2 = Service::factory()->create([
            'duration' => 45,
            'preparation_time' => 10,
            'cleanup_time' => 5,
        ]);

        $response = $this->postJson('/api/client/time-slots/calculate-duration', [
            'services' => [$service1->id, $service2->id],
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'total_duration' => 100, // (30+5+5) + (45+10+5)
                        'total_hours' => 1.67,
                        'services_count' => 2
                    ]
                ]);
    }

    public function test_validation_errors()
    {
        // Test sans salon_id
        $response = $this->getJson('/api/client/time-slots/available');
        $response->assertStatus(422);

        // Test avec salon_id invalide
        $response = $this->getJson('/api/client/time-slots/available?salon_id=999999');
        $response->assertStatus(422);

        // Test avec duration négative
        $response = $this->getJson('/api/client/time-slots/available?' . http_build_query([
            'salon_id' => $this->salon->id,
            'duration' => -10,
        ]));
        $response->assertStatus(422);
    }
}
