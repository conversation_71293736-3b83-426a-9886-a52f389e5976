<?php
/**
 * Test simple pour l'API de recherche de salons
 * Usage: php test_search.php
 */

// Configuration
$baseUrl = 'http://127.0.0.1:8000/api';

echo "🧪 Test de l'API de recherche de salons\n";
echo "=====================================\n\n";

// Test 1: Récupérer tous les services
echo "1. Test récupération des services...\n";
$servicesUrl = $baseUrl . '/services';
$servicesResponse = file_get_contents($servicesUrl);
$services = json_decode($servicesResponse, true);

if ($services) {
    echo "✅ Services récupérés: " . count($services) . " services trouvés\n";
    foreach ($services as $service) {
        echo "   - {$service['name']} (ID: {$service['id']})\n";
    }
} else {
    echo "❌ Erreur lors de la récupération des services\n";
}

echo "\n";

// Test 2: Recherche par lieu
echo "2. Test recherche par lieu (Paris)...\n";
$searchUrl = $baseUrl . '/salons/search?lieu=Paris';
$searchResponse = file_get_contents($searchUrl);
$searchResults = json_decode($searchResponse, true);

if ($searchResults && isset($searchResults['success'])) {
    echo "✅ Recherche par lieu réussie\n";
    echo "   Résultats: " . count($searchResults['data']) . " salon(s) trouvé(s)\n";
    foreach ($searchResults['data'] as $salon) {
        echo "   - {$salon['name']} ({$salon['address']})\n";
    }
} else {
    echo "❌ Erreur lors de la recherche par lieu\n";
}

echo "\n";

// Test 3: Recherche par service
if (!empty($services)) {
    $firstServiceId = $services[0]['id'];
    echo "3. Test recherche par service (ID: {$firstServiceId})...\n";
    $searchUrl = $baseUrl . '/salons/search?service=' . $firstServiceId;
    $searchResponse = file_get_contents($searchUrl);
    $searchResults = json_decode($searchResponse, true);

    if ($searchResults && isset($searchResults['success'])) {
        echo "✅ Recherche par service réussie\n";
        echo "   Résultats: " . count($searchResults['data']) . " salon(s) trouvé(s)\n";
    } else {
        echo "❌ Erreur lors de la recherche par service\n";
    }
}

echo "\n";

// Test 4: Recherche par date
echo "4. Test recherche par date (2025-01-15)...\n";
$searchUrl = $baseUrl . '/salons/search?date=2025-01-15';
$searchResponse = file_get_contents($searchUrl);
$searchResults = json_decode($searchResponse, true);

if ($searchResults && isset($searchResults['success'])) {
    echo "✅ Recherche par date réussie\n";
    echo "   Résultats: " . count($searchResults['data']) . " salon(s) trouvé(s)\n";
} else {
    echo "❌ Erreur lors de la recherche par date\n";
}

echo "\n";

// Test 5: Recherche combinée
echo "5. Test recherche combinée (lieu + service)...\n";
$searchUrl = $baseUrl . '/salons/search?lieu=Paris&service=' . ($services[0]['id'] ?? 1);
$searchResponse = file_get_contents($searchUrl);
$searchResults = json_decode($searchResponse, true);

if ($searchResults && isset($searchResults['success'])) {
    echo "✅ Recherche combinée réussie\n";
    echo "   Résultats: " . count($searchResults['data']) . " salon(s) trouvé(s)\n";
    echo "   Filtres appliqués: " . json_encode($searchResults['filters']) . "\n";
} else {
    echo "❌ Erreur lors de la recherche combinée\n";
}

echo "\n";
echo "🎉 Tests terminés!\n"; 