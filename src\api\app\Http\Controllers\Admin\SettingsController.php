<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = Setting::all()->pluck('value', 'key');
        return response()->json($settings);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'site_name' => 'sometimes|string|max:255',
            'support_email' => 'sometimes|email',
            'max_advance_booking_days' => 'sometimes|integer|min:1',
            'cancellation_period_hours' => 'sometimes|integer|min:1',
            'enable_email_notifications' => 'sometimes|boolean',
            'enable_sms_notifications' => 'sometimes|boolean',
            'enable_push_notifications' => 'sometimes|boolean',
            'google_maps_api_key' => 'sometimes|string',
            'stripe_public_key' => 'sometimes|string',
            'stripe_secret_key' => 'sometimes|string',
            'default_currency' => 'sometimes|string|size:3',
            'timezone' => 'sometimes|string',
            'maintenance_mode' => 'sometimes|boolean',
        ]);

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return response()->json([
            'message' => 'Settings updated successfully',
            'settings' => Setting::all()->pluck('value', 'key'),
        ]);
    }

    public function getEmailSettings()
    {
        $emailSettings = [
            'smtp_host' => Setting::where('key', 'smtp_host')->first()?->value,
            'smtp_port' => Setting::where('key', 'smtp_port')->first()?->value,
            'smtp_username' => Setting::where('key', 'smtp_username')->first()?->value,
            'smtp_encryption' => Setting::where('key', 'smtp_encryption')->first()?->value,
            'from_address' => Setting::where('key', 'from_address')->first()?->value,
            'from_name' => Setting::where('key', 'from_name')->first()?->value,
        ];

        return response()->json($emailSettings);
    }

    public function updateEmailSettings(Request $request)
    {
        $validated = $request->validate([
            'smtp_host' => 'required|string',
            'smtp_port' => 'required|integer',
            'smtp_username' => 'required|string',
            'smtp_password' => 'required|string',
            'smtp_encryption' => 'required|in:tls,ssl',
            'from_address' => 'required|email',
            'from_name' => 'required|string',
        ]);

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return response()->json([
            'message' => 'Email settings updated successfully'
        ]);
    }

    public function getSMSSettings()
    {
        $smsSettings = [
            'sms_provider' => Setting::where('key', 'sms_provider')->first()?->value,
            'sms_api_key' => Setting::where('key', 'sms_api_key')->first()?->value,
            'sms_from_number' => Setting::where('key', 'sms_from_number')->first()?->value,
        ];

        return response()->json($smsSettings);
    }

    public function updateSMSSettings(Request $request)
    {
        $validated = $request->validate([
            'sms_provider' => 'required|in:twilio,nexmo',
            'sms_api_key' => 'required|string',
            'sms_api_secret' => 'required|string',
            'sms_from_number' => 'required|string',
        ]);

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return response()->json([
            'message' => 'SMS settings updated successfully'
        ]);
    }

    public function getPaymentSettings()
    {
        $paymentSettings = [
            'stripe_public_key' => Setting::where('key', 'stripe_public_key')->first()?->value,
            'stripe_secret_key' => Setting::where('key', 'stripe_secret_key')->first()?->value,
            'currency' => Setting::where('key', 'currency')->first()?->value,
            'tax_rate' => Setting::where('key', 'tax_rate')->first()?->value,
        ];

        return response()->json($paymentSettings);
    }

    public function updatePaymentSettings(Request $request)
    {
        $validated = $request->validate([
            'stripe_public_key' => 'required|string',
            'stripe_secret_key' => 'required|string',
            'currency' => 'required|string|size:3',
            'tax_rate' => 'required|numeric|min:0|max:100',
        ]);

        foreach ($validated as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return response()->json([
            'message' => 'Payment settings updated successfully'
        ]);
    }
}