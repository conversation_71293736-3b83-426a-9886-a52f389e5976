<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TimeOffRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'hairdresserId',
        'start_date',
        'end_date',
        'reason',
        'status',
        'response_note',
        'approved_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class, 'hairdresserId');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function overlapsWithBookings()
    {
        return $this->hairdresser->bookings()
            ->where('status', 'confirmed')
            ->whereBetween('dateTime', [$this->start_date, $this->end_date])
            ->exists();
    }
}