<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;

class CustomerManagementController extends Controller
{
    public function index(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $query = User::where('role', 'client')
            ->whereHas('bookings', function ($query) use ($salon) {
                $query->where('salonId', $salon->id);
            });

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('firstName', 'like', "%{$search}%")
                  ->orWhere('lastName', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $customers = $query->paginate(10);
        return UserResource::collection($customers);
    }

    public function show(User $customer)
    {
        $salon = Auth::user()->managedSalon;
        
        $customerData = [
            'profile' => new UserResource($customer),
            'bookings' => $customer->bookings()
                ->where('salonId', $salon->id)
                ->with(['services', 'hairdresser'])
                ->get(),
            'preferences' => $customer->preferences()
                ->where('salonId', $salon->id)
                ->first(),
            'total_spent' => $customer->bookings()
                ->where('salonId', $salon->id)
                ->where('status', 'completed')
                ->sum('total_amount'),
        ];

        return response()->json($customerData);
    }

    public function updatePreferences(Request $request, User $customer)
    {
        $salon = Auth::user()->managedSalon;

        $validated = $request->validate([
            'preferred_hairdresserid' => 'sometimes|exists:users,id',
            'preferred_services' => 'sometimes|array',
            'preferred_services.*' => 'exists:services,id',
            'notes' => 'sometimes|string',
        ]);

        $preferences = $customer->preferences()
            ->where('salonId', $salon->id)
            ->updateOrCreate(
                ['salonId' => $salon->id],
                $validated
            );

        return response()->json($preferences);
    }

    public function getCustomerAnalytics(Request $request)
    {
        $salon = Auth::user()->managedSalon;
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $analytics = [
            'new_customers' => User::where('role', 'client')
                ->whereHas('bookings', function ($query) use ($salon) {
                    $query->where('salonId', $salon->id);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'returning_customers' => User::where('role', 'client')
                ->whereHas('bookings', function ($query) use ($salon, $startDate, $endDate) {
                    $query->where('salonId', $salon->id)
                          ->whereBetween('dateTime', [$startDate, $endDate])
                          ->havingRaw('COUNT(*) > 1');
                })
                ->count(),
            'top_customers' => User::where('role', 'client')
                ->whereHas('bookings', function ($query) use ($salon, $startDate, $endDate) {
                    $query->where('salonId', $salon->id)
                          ->whereBetween('dateTime', [$startDate, $endDate]);
                })
                ->withCount(['bookings' => function ($query) use ($salon, $startDate, $endDate) {
                    $query->where('salonId', $salon->id)
                          ->whereBetween('dateTime', [$startDate, $endDate]);
                }])
                ->orderBy('bookings_count', 'desc')
                ->take(10)
                ->get(),
        ];

        return response()->json($analytics);
    }

    public function sendPromotionalMessage(Request $request)
    {
        $salon = Auth::user()->managedSalon;

        $validated = $request->validate([
            'customerids' => 'required|array',
            'customerids.*' => 'exists:users,id',
            'message' => 'required|string',
            'type' => 'required|in:email,sms',
        ]);

        $customers = User::whereIn('id', $validated['customerids'])->get();

        // Implementation depends on your notification system
        foreach ($customers as $customer) {
            // Send promotional message
        }

        return response()->json(['message' => 'Promotional message sent successfully']);
    }
}