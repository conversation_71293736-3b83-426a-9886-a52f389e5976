<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSlot;
use App\Models\Booking;
use App\Models\AvailabilityTemplate;
use App\Models\Hairdresser;
use Carbon\Carbon;

class ValidateTimeSlots extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'timeslots:validate 
                            {--fix : Tenter de corriger automatiquement les problèmes}';

    /**
     * The console command description.
     */
    protected $description = 'Valider l\'intégrité du système de créneaux horaires';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Validation du système de créneaux horaires');
        
        $fix = $this->option('fix');
        $issues = [];

        // Validation 1: Créneaux orphelins
        $issues = array_merge($issues, $this->validateOrphanedSlots($fix));

        // Validation 2: Réservations sans créneaux
        $issues = array_merge($issues, $this->validateBookingsWithoutSlots($fix));

        // Validation 3: Conflits de créneaux
        $issues = array_merge($issues, $this->validateSlotConflicts($fix));

        // Validation 4: Templates sans créneaux
        $issues = array_merge($issues, $this->validateTemplatesWithoutSlots($fix));

        // Validation 5: Créneaux dans le passé
        $issues = array_merge($issues, $this->validatePastSlots($fix));

        // Rapport final
        $this->displayValidationReport($issues);

        return empty($issues) ? 0 : 1;
    }

    /**
     * Valider les créneaux orphelins
     */
    private function validateOrphanedSlots($fix)
    {
        $this->info('🔍 Vérification des créneaux orphelins...');
        
        $orphanedSlots = TimeSlot::whereDoesntHave('hairdresser')->get();
        $issues = [];

        if ($orphanedSlots->count() > 0) {
            $issues[] = [
                'type' => 'Créneaux orphelins',
                'count' => $orphanedSlots->count(),
                'description' => 'Créneaux sans coiffeur associé',
                'severity' => 'high'
            ];

            if ($fix) {
                $deleted = $orphanedSlots->count();
                TimeSlot::whereDoesntHave('hairdresser')->delete();
                $this->warn("  🔧 {$deleted} créneaux orphelins supprimés");
            }
        }

        return $issues;
    }

    /**
     * Valider les réservations sans créneaux
     */
    private function validateBookingsWithoutSlots($fix)
    {
        $this->info('🔍 Vérification des réservations sans créneaux...');
        
        $bookingsWithoutSlots = Booking::whereNull('time_slot_id')
            ->whereNotNull('dateTime')
            ->where('dateTime', '>=', now())
            ->whereIn('status', ['pending', 'confirmed'])
            ->get();

        $issues = [];

        if ($bookingsWithoutSlots->count() > 0) {
            $issues[] = [
                'type' => 'Réservations sans créneaux',
                'count' => $bookingsWithoutSlots->count(),
                'description' => 'Réservations futures non migrées vers le système de créneaux',
                'severity' => 'medium'
            ];

            if ($fix) {
                $this->warn("  🔧 Utilisez la commande 'timeslots:migrate' pour corriger ce problème");
            }
        }

        return $issues;
    }

    /**
     * Valider les conflits de créneaux
     */
    private function validateSlotConflicts($fix)
    {
        $this->info('🔍 Vérification des conflits de créneaux...');
        
        $conflicts = [];
        $hairdressers = Hairdresser::all();

        foreach ($hairdressers as $hairdresser) {
            $slots = $hairdresser->timeSlots()
                ->where('date', '>=', now()->toDateString())
                ->orderBy('date')
                ->orderBy('start_time')
                ->get();

            for ($i = 0; $i < $slots->count() - 1; $i++) {
                $currentSlot = $slots[$i];
                $nextSlot = $slots[$i + 1];

                if ($currentSlot->date === $nextSlot->date &&
                    $currentSlot->end_time > $nextSlot->start_time) {
                    $conflicts[] = [
                        'hairdresser_id' => $hairdresser->id,
                        'slot1_id' => $currentSlot->id,
                        'slot2_id' => $nextSlot->id,
                        'date' => $currentSlot->date
                    ];
                }
            }
        }

        $issues = [];

        if (count($conflicts) > 0) {
            $issues[] = [
                'type' => 'Conflits de créneaux',
                'count' => count($conflicts),
                'description' => 'Créneaux qui se chevauchent pour le même coiffeur',
                'severity' => 'high'
            ];

            if ($fix) {
                foreach ($conflicts as $conflict) {
                    // Supprimer le deuxième créneau en conflit
                    TimeSlot::find($conflict['slot2_id'])->delete();
                }
                $this->warn("  🔧 " . count($conflicts) . " conflits résolus en supprimant les créneaux en double");
            }
        }

        return $issues;
    }

    /**
     * Valider les templates sans créneaux
     */
    private function validateTemplatesWithoutSlots($fix)
    {
        $this->info('🔍 Vérification des templates sans créneaux...');
        
        $templatesWithoutSlots = AvailabilityTemplate::whereDoesntHave('timeSlots', function ($query) {
            $query->where('date', '>=', now()->toDateString());
        })->where('is_active', true)->get();

        $issues = [];

        if ($templatesWithoutSlots->count() > 0) {
            $issues[] = [
                'type' => 'Templates sans créneaux',
                'count' => $templatesWithoutSlots->count(),
                'description' => 'Templates actifs sans créneaux générés',
                'severity' => 'low'
            ];

            if ($fix) {
                $this->warn("  🔧 Utilisez la commande 'timeslots:generate' pour générer les créneaux manquants");
            }
        }

        return $issues;
    }

    /**
     * Valider les créneaux dans le passé
     */
    private function validatePastSlots($fix)
    {
        $this->info('🔍 Vérification des créneaux dans le passé...');
        
        $pastSlots = TimeSlot::where('date', '<', now()->toDateString())
            ->where('status', 'available')
            ->get();

        $issues = [];

        if ($pastSlots->count() > 0) {
            $issues[] = [
                'type' => 'Créneaux passés disponibles',
                'count' => $pastSlots->count(),
                'description' => 'Créneaux dans le passé encore marqués comme disponibles',
                'severity' => 'low'
            ];

            if ($fix) {
                $deleted = $pastSlots->count();
                TimeSlot::where('date', '<', now()->toDateString())
                    ->where('status', 'available')
                    ->delete();
                $this->warn("  🔧 {$deleted} créneaux passés supprimés");
            }
        }

        return $issues;
    }

    /**
     * Afficher le rapport de validation
     */
    private function displayValidationReport($issues)
    {
        $this->info('');
        $this->info('📊 RAPPORT DE VALIDATION');
        $this->info('========================');

        if (empty($issues)) {
            $this->info('✅ Aucun problème détecté ! Le système est en bon état.');
            return;
        }

        $this->table(
            ['Type de problème', 'Nombre', 'Description', 'Gravité'],
            array_map(function ($issue) {
                $severity = match($issue['severity']) {
                    'high' => '🔴 Élevée',
                    'medium' => '🟡 Moyenne',
                    'low' => '🟢 Faible',
                    default => '⚪ Inconnue'
                };

                return [
                    $issue['type'],
                    $issue['count'],
                    $issue['description'],
                    $severity
                ];
            }, $issues)
        );

        $highIssues = array_filter($issues, fn($issue) => $issue['severity'] === 'high');
        $mediumIssues = array_filter($issues, fn($issue) => $issue['severity'] === 'medium');

        if (!empty($highIssues)) {
            $this->error('❌ Problèmes critiques détectés ! Correction recommandée.');
        } elseif (!empty($mediumIssues)) {
            $this->warn('⚠️  Problèmes modérés détectés. Correction suggérée.');
        } else {
            $this->info('ℹ️  Problèmes mineurs détectés. Correction optionnelle.');
        }

        $this->info('');
        $this->info('💡 Utilisez --fix pour tenter de corriger automatiquement les problèmes.');
    }
}
