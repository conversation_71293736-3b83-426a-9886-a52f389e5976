<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $roles
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $roles)
    {
        if (!$request->user()) {
            return response()->json([
                'message' => 'Unauthorized access'
            ], 403);
        }

        $allowedRoles = explode('|', $roles);

        if (!in_array($request->user()->role, $allowedRoles)) {
            return response()->json([
                'message' => 'Unauthorized access'
            ], 403);
        }

        return $next($request);
    }
}
