<?php

namespace App\Http\Controllers\Hairdresser;

use App\Http\Controllers\Controller;
use App\Services\TimeSlotService;
use App\Services\AvailabilityService;
use App\Models\AvailabilityTemplate;
use App\Models\SlotException;
use App\Models\TimeSlot;
use App\Models\Hairdresser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TimeSlotController extends Controller
{
    protected $timeSlotService;
    protected $availabilityService;

    public function __construct(TimeSlotService $timeSlotService, AvailabilityService $availabilityService)
    {
        $this->timeSlotService = $timeSlotService;
        $this->availabilityService = $availabilityService;
    }

    /**
     * Obtenir les templates de disponibilité du coiffeur
     */
    public function getAvailabilityTemplates(): JsonResponse
    {
        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $templates = $hairdresser->availabilityTemplates()
                ->orderBy('day_of_week')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $templates
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des templates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Créer ou mettre à jour un template de disponibilité
     */
    public function storeAvailabilityTemplate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'day_of_week' => 'required|integer|between:0,6',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'slot_duration' => 'required|integer|min:15|max:240',
            'buffer_time' => 'required|integer|min:0|max:60',
            'max_advance_booking' => 'required|integer|min:1|max:365',
            'is_active' => 'boolean'
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $validated['hairdresser_id'] = $hairdresser->id;
            $validated['salon_id'] = $hairdresser->salonId;

            $template = $this->availabilityService->createOrUpdateTemplate($validated);

            return response()->json([
                'success' => true,
                'message' => 'Template de disponibilité mis à jour avec succès',
                'data' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour du template',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir le planning du coiffeur
     */
    public function getSchedule(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->toDateString();
            $endDate = $validated['end_date'] ?? now()->addDays(7)->toDateString();

            $availability = $this->availabilityService->getHairdresserAvailability(
                $hairdresser,
                $startDate,
                $endDate
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'availability' => $availability,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du planning',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Créer une exception (congé, pause, etc.)
     */
    public function createException(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'type' => 'required|in:unavailable,break,lunch,vacation,sick,training,custom',
            'reason' => 'nullable|string|max:255',
            'recurring' => 'boolean',
            'recurring_pattern' => 'nullable|array',
            'recurring_until' => 'nullable|date|after:date',
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $validated['hairdresser_id'] = $hairdresser->id;

            if ($validated['recurring'] ?? false) {
                $exception = $this->availabilityService->createRecurringException($validated);
            } else {
                $exception = $this->availabilityService->createException($validated);
            }

            return response()->json([
                'success' => true,
                'message' => 'Exception créée avec succès',
                'data' => $exception
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création de l\'exception',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les exceptions du coiffeur
     */
    public function getExceptions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'type' => 'nullable|in:unavailable,break,lunch,vacation,sick,training,custom',
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $query = $hairdresser->slotExceptions()->active();

            if (isset($validated['start_date']) && isset($validated['end_date'])) {
                $query->forDateRange($validated['start_date'], $validated['end_date']);
            }

            if (isset($validated['type'])) {
                $query->ofType($validated['type']);
            }

            $exceptions = $query->orderBy('date')->orderBy('start_time')->get();

            return response()->json([
                'success' => true,
                'data' => $exceptions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des exceptions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer une exception
     */
    public function deleteException(SlotException $exception): JsonResponse
    {
        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser || $exception->hairdresser_id !== $hairdresser->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Exception non trouvée ou non autorisée'
                ], 404);
            }

            $this->availabilityService->deleteException($exception);

            return response()->json([
                'success' => true,
                'message' => 'Exception supprimée avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression de l\'exception',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bloquer/débloquer des créneaux manuellement
     */
    public function toggleSlotStatus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'time_slot_ids' => 'required|array',
            'time_slot_ids.*' => 'exists:time_slots,id',
            'action' => 'required|in:block,unblock',
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $timeSlots = TimeSlot::whereIn('id', $validated['time_slot_ids'])
                ->where('hairdresser_id', $hairdresser->id)
                ->get();

            if ($timeSlots->count() !== count($validated['time_slot_ids'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certains créneaux n\'ont pas été trouvés ou ne vous appartiennent pas'
                ], 404);
            }

            $updatedCount = 0;

            foreach ($timeSlots as $slot) {
                if ($validated['action'] === 'block' && $slot->status === 'available') {
                    $slot->block($validated['reason'] ?? 'Bloqué manuellement');
                    $updatedCount++;
                } elseif ($validated['action'] === 'unblock' && $slot->status === 'blocked') {
                    $slot->markAsAvailable();
                    $updatedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "{$updatedCount} créneaux mis à jour",
                'data' => [
                    'updated_count' => $updatedCount,
                    'action' => $validated['action']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des créneaux',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques du coiffeur
     */
    public function getStats(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $hairdresser = Auth::user()->hairdresser;
            
            if (!$hairdresser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Profil coiffeur non trouvé'
                ], 404);
            }

            $startDate = $validated['start_date'] ?? now()->startOfMonth()->toDateString();
            $endDate = $validated['end_date'] ?? now()->endOfMonth()->toDateString();

            $stats = $this->timeSlotService->getOccupancyStats(
                $hairdresser->salonId,
                $startDate,
                $endDate,
                $hairdresser->id
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'stats' => $stats,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
