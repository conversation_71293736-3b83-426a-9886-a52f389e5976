<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Hairdresser extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'userId',
        'salonId',
        'specialties',
        'availability',
        'rating',
        'active',
    ];

    protected $casts = [
        'specialties' => 'array',
        'availability' => 'array',
        'rating' => 'decimal:2',
        'active' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'userId');
    }

    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salonId');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'hairdresserId');
    }

    public function timeOffRequests()
    {
        return $this->hasMany(TimeOffRequest::class, 'hairdresserId');
    }

    public function clientNotes()
    {
        return $this->hasMany(ClientNote::class, 'hairdresserId');
    }

    public function calculateRating()
    {
        $this->rating = $this->bookings()->avg('rating') ?? 0;
        $this->save();
    }

    public function isAvailable($dateTime)
    {
        // Check if the datetime falls within availability
        // and there are no existing bookings or time off requests
        return true; // Implementation needed
    }
}