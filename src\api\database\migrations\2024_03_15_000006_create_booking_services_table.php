<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('booking_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bookingId')->constrained('bookings')->onDelete('cascade');
            $table->foreignId('serviceId')->constrained('services')->onDelete('cascade');
            $table->decimal('price_at_time', 10, 2);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('booking_services');
    }
};
