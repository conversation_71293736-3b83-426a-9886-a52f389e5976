<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('salonId')->nullable(); // Déclarez la colonne ici
            $table->foreign('salonId')->references('id')->on('salons')->onUpdate('cascade')->onDelete('cascade');
            $table->string('firstName');
            $table->string('lastName');
            $table->string('email')->unique();
            $table->string('password');
            $table->string('phone')->nullable();
            $table->enum('gender', ['male', 'female', 'other']);
            $table->string('photoUrl')->nullable();
            $table->enum('role', ['admin', 'owner', 'manager', 'hairdresser', 'client']);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->integer('loyaltyPoints')->default(0);
            $table->integer('lifetimeLoyaltyPoints')->default(0);
            $table->string('specialties')->nullable();
            $table->json('availability')->nullable();
            $table->timestamp('email_verified_at')->nullable(); // Ajoutez cette ligne
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('users');
    }
};