<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class SlotException extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'hairdresser_id',
        'date',
        'start_time',
        'end_time',
        'type',
        'reason',
        'recurring',
        'recurring_pattern',
        'recurring_until',
        'is_active',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'recurring' => 'boolean',
        'recurring_pattern' => 'array',
        'recurring_until' => 'date',
        'is_active' => 'boolean',
    ];

    const TYPE_UNAVAILABLE = 'unavailable';
    const TYPE_BREAK = 'break';
    const TYPE_LUNCH = 'lunch';
    const TYPE_VACATION = 'vacation';
    const TYPE_SICK = 'sick';
    const TYPE_TRAINING = 'training';
    const TYPE_CUSTOM = 'custom';

    /**
     * Relations
     */
    public function hairdresser()
    {
        return $this->belongsTo(Hairdresser::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    public function scopeForHairdresser($query, $hairdresserId)
    {
        return $query->where('hairdresser_id', $hairdresserId);
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeRecurring($query)
    {
        return $query->where('recurring', true);
    }

    public function scopeNonRecurring($query)
    {
        return $query->where('recurring', false);
    }

    public function scopeInTimeRange($query, $startTime, $endTime)
    {
        return $query->where(function ($q) use ($startTime, $endTime) {
            $q->where(function ($subQ) use ($startTime, $endTime) {
                // Exception qui commence avant et finit après la plage
                $subQ->where('start_time', '<=', $startTime)
                     ->where('end_time', '>=', $endTime);
            })->orWhere(function ($subQ) use ($startTime, $endTime) {
                // Exception qui chevauche le début
                $subQ->where('start_time', '<', $endTime)
                     ->where('end_time', '>', $startTime);
            });
        });
    }

    /**
     * Méthodes utilitaires
     */
    public function isAllDay()
    {
        return is_null($this->start_time) && is_null($this->end_time);
    }

    public function getTypeName()
    {
        $types = [
            self::TYPE_UNAVAILABLE => 'Indisponible',
            self::TYPE_BREAK => 'Pause',
            self::TYPE_LUNCH => 'Déjeuner',
            self::TYPE_VACATION => 'Congés',
            self::TYPE_SICK => 'Maladie',
            self::TYPE_TRAINING => 'Formation',
            self::TYPE_CUSTOM => 'Personnalisé',
        ];

        return $types[$this->type] ?? 'Inconnu';
    }

    public function getFullDateTime()
    {
        if ($this->isAllDay()) {
            return Carbon::parse($this->date);
        }

        return Carbon::parse($this->date . ' ' . $this->start_time);
    }

    public function getFullEndDateTime()
    {
        if ($this->isAllDay()) {
            return Carbon::parse($this->date)->endOfDay();
        }

        return Carbon::parse($this->date . ' ' . $this->end_time);
    }

    public function affectsTimeSlot(TimeSlot $timeSlot)
    {
        // Si l'exception n'est pas active ou pas pour la même date
        if (!$this->is_active || $this->date !== $timeSlot->date) {
            return false;
        }

        // Si c'est une exception toute la journée
        if ($this->isAllDay()) {
            return true;
        }

        // Vérifier le chevauchement des heures
        return $this->hasTimeOverlapWith($timeSlot->start_time, $timeSlot->end_time);
    }

    public function hasTimeOverlapWith($startTime, $endTime)
    {
        if ($this->isAllDay()) {
            return true;
        }

        $exceptionStart = Carbon::parse($this->start_time);
        $exceptionEnd = Carbon::parse($this->end_time);
        $checkStart = Carbon::parse($startTime);
        $checkEnd = Carbon::parse($endTime);

        return $checkStart->lt($exceptionEnd) && $checkEnd->gt($exceptionStart);
    }

    public function isRecurringActive($date = null)
    {
        if (!$this->recurring) {
            return false;
        }

        $checkDate = $date ? Carbon::parse($date) : now();

        // Vérifier si la récurrence est encore valide
        if ($this->recurring_until && $checkDate->gt($this->recurring_until)) {
            return false;
        }

        return $this->is_active;
    }

    public function getRecurringDates($startDate, $endDate)
    {
        if (!$this->recurring || !$this->recurring_pattern) {
            return [];
        }

        $dates = [];
        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        // Si une date de fin de récurrence est définie, l'utiliser si elle est plus proche
        if ($this->recurring_until) {
            $end = $end->min(Carbon::parse($this->recurring_until));
        }

        $pattern = $this->recurring_pattern;

        while ($current->lte($end)) {
            if ($this->matchesRecurringPattern($current, $pattern)) {
                $dates[] = $current->toDateString();
            }
            $current->addDay();
        }

        return $dates;
    }

    private function matchesRecurringPattern($date, $pattern)
    {
        $type = $pattern['type'] ?? 'weekly';

        switch ($type) {
            case 'daily':
                return true;

            case 'weekly':
                $days = $pattern['days'] ?? [$this->date->dayOfWeek];
                return in_array($date->dayOfWeek, $days);

            case 'monthly':
                $dayOfMonth = $pattern['day_of_month'] ?? $this->date->day;
                return $date->day === $dayOfMonth;

            default:
                return false;
        }
    }

    public function getDurationInMinutes()
    {
        if ($this->isAllDay()) {
            return 24 * 60; // 1440 minutes
        }

        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);

        return $end->diffInMinutes($start);
    }

    public function getFormattedTimeRange()
    {
        if ($this->isAllDay()) {
            return 'Toute la journée';
        }

        return Carbon::parse($this->start_time)->format('H:i') . ' - ' . 
               Carbon::parse($this->end_time)->format('H:i');
    }
}
