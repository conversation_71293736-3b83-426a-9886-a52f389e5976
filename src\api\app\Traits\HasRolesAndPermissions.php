<?php

namespace App\Traits;

use App\Models\Role;
use App\Models\Permission;

trait HasRolesAndPermissions
{
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function hasRole($role)
    {
        return $this->role->name === $role;
    }

    public function hasPermission($permission)
    {
        return $this->role->permissions->contains('name', $permission);
    }

    public function hasAnyPermission($permissions)
    {
        return $this->role->permissions
            ->pluck('name')
            ->intersect($permissions)
            ->isNotEmpty();
    }

    public function hasAllPermissions($permissions)
    {
        return $this->role->permissions
            ->pluck('name')
            ->intersect($permissions)
            ->count() === count($permissions);
    }
}